/**
 * YouRoute - Multi-App Routing Worker
 *
 * 智能路由分发器，将不同路径的请求分发到对应的 Cloudflare Pages 应用
 * 或主服务器。支持多环境部署和配置化路由规则。
 */

import { checkUserAuthentication } from './auth';
import { routeToApp, routeToDefault } from './handlers';
import { findMatchingRoute, getAllPaths, getEnvironmentInfo } from './routes';
import type { Env } from './types';
import { YOUROUTE_VERSION } from './types';
import { addCorsHeaders, createRedirectResponse, log } from './utils';

/**
 * 主要的 Fetch 处理器
 */
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: addCorsHeaders(new Headers()),
      });
    }

    // 健康检查端点
    if (pathname === '/_health') {
      return new Response(
        JSON.stringify({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          environment: env.ENVIRONMENT || 'production',
          version: YOUROUTE_VERSION,
          routes: getAllPaths(),
          apps: getEnvironmentInfo(env.ENVIRONMENT || 'production'),
        }),
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Routed-By': 'youroute-worker',
            'X-Youroute-Version': YOUROUTE_VERSION,
          },
        },
      );
    }

    // 特殊处理：检查根路径 "/" 的用户认证状态
    if (pathname === '/' && request.method === 'GET') {
      try {
        const isAuthenticated = checkUserAuthentication(request, env);
        if (isAuthenticated) {
          // 用户已认证，重定向到 /boards
          log('info', 'User authenticated, redirecting to /boards', env);
          return createRedirectResponse('/boards', env);
        }
        // 用户未认证，继续正常的路由逻辑（通常会路由到 youhome）
      } catch (error) {
        // 认证检查失败，记录错误但继续正常路由
        log('warn', `Auth check failed for root path: ${error}`, env);
      }
    }

    // 查找匹配的应用路由
    const matchedRoute = findMatchingRoute(pathname);

    if (matchedRoute) {
      log('info', `Matched route: ${matchedRoute.name} for path: ${pathname}`, env);
      return routeToApp(request, matchedRoute, env);
    }

    // 默认路由到 youweb
    log('info', `No route matched, routing to default app: ${pathname}`, env);
    return routeToDefault(request, env);
  },
};
