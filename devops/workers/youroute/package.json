{"name": "youroute", "version": "1.0.0", "private": true, "type": "module", "description": "Multi-app routing worker for youniverse monorepo", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:preview": "wrangler deploy --env preview", "deploy:production": "wrangler deploy --env production", "build": "tsc", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "type-check": "tsc --noEmit"}, "devDependencies": {"@cloudflare/workers-types": "catalog:", "@types/node": "catalog:", "typescript": "catalog:", "wrangler": "catalog:"}, "dependencies": {"@repo/server-common": "workspace:*"}}