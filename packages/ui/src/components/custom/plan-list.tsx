import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';

export const PlanList: React.FC = () => {
  return (
    <Tabs defaultValue="annual" className="w-full">
      <TabsList variant="pills" className="w-[300px] mx-auto">
        <TabsTrigger value="annual">Annual</TabsTrigger>
        <TabsTrigger value="monthly">Monthly</TabsTrigger>
      </TabsList>
      <TabsContent value="annual">
        <div className="flex flex-col items-center justify-center">
          <div className="headline2">Annual</div>
        </div>
      </TabsContent>
      <TabsContent value="monthly">
        <div className="flex flex-col items-center justify-center">
          <div className="headline2">Monthly</div>
        </div>
      </TabsContent>
    </Tabs>
  );
};
