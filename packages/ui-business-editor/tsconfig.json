{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@repo/ui/*": ["../../packages/ui/src/*"], "@repo/editor-common": ["../../packages/editor-common/src/index"]}, "plugins": [{"name": "next"}], "target": "ES2017"}}