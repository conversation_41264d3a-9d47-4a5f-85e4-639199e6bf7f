import type { AnyExtension } from '@tiptap/core';
import type { Editor, JSONContent, UseEditorOptions } from '@tiptap/react';
import type { Doc } from 'yjs';

export type ExtensionInitContent = {
  ydoc: Doc;
};

export type ThoughtEditorProps = {
  id: string;
  className?: string;
  content: string | JSONContent;
  onReady?: (params: ThoughtEditorReadyParams) => void;
  storeOptions?: {
    localIndexDB?: boolean;
  };
  editorOptions?: Omit<UseEditorOptions, 'extensions'>;
  extensions?: AnyExtension[] | ((content: ExtensionInitContent) => AnyExtension[]);
};

export type ThoughtEditorReadyParams = {
  editor: Editor;
  ydoc: Doc;
};
