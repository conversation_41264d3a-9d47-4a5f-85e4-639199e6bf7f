import { DiffBlockOptions, LinkOptions, MermaidOptions } from '@repo/editor-common';
import { PlaceholderOptions } from '@tiptap/extension-placeholder';
import {
  CharacterCountOptions,
  ImageExtensionOptions,
  KeyDownOptions,
  LocalCollaborationOptions,
} from '../extension';

export type EditorExtensionOptions = {
  keyDownOptions?: KeyDownOptions;
  characterCountOptions?: CharacterCountOptions;
  imageOptions?: Partial<ImageExtensionOptions>;
  localCollaborationOptions?: LocalCollaborationOptions;
  linkOptions?: Partial<LinkOptions>;
  placeholderOptions?: Partial<PlaceholderOptions>;
  diffBlockOptions?: Partial<DiffBlockOptions>;
  mermaidOptions?: Partial<MermaidOptions>;
};
