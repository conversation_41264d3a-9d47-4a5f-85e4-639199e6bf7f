import { Audio, CodeBlock, Divider, Image, Mermaid, SVG, Table } from '@repo/editor-common';
import { Dropcursor } from '@tiptap/extension-dropcursor';
import { FileHandler } from '@tiptap/extension-file-handler';
import { Gapcursor } from '@tiptap/extension-gapcursor';
import { Placeholder } from '@tiptap/extension-placeholder';
import {
  AutoEmptyLine,
  CharacterCount,
  ClipboardAdapter,
  EditorStatusClassExtension,
  HeadingHandler,
  IndentHandler,
  KeyDownCustomExtension,
  ListHandler,
  NodeNavigationAdapter,
  SafariTableAdapterExtension,
  ScrollCursorIntoView,
  SelectionDisplay,
  uploadImages,
} from '../extension';
import type { EditorExtensionOptions } from './type';

// 编辑器公共的功能性扩展
export const getCommonFunctionExtensionList = (options?: EditorExtensionOptions) => {
  return [
    AutoEmptyLine,
    ScrollCursorIntoView,
    CharacterCount.configure(options?.characterCountOptions),
    ClipboardAdapter,
    SafariTableAdapterExtension,
    SelectionDisplay,
    Dropcursor,
    Gapcursor,
    EditorStatusClassExtension,
    ListHandler,
    HeadingHandler,
    IndentHandler,
    NodeNavigationAdapter.configure({
      nodeConfigs: [
        // 完整模式：支持节点内部逻辑
        { nodeType: Mermaid.name, checkInside: true },
        { nodeType: Image.name, checkInside: true },
        { nodeType: Divider.name, checkInside: true },
        { nodeType: CodeBlock.name, checkInside: true },
        { nodeType: Table.name, checkInside: true },
        // 简化模式：只处理前一个节点
        { nodeType: SVG.name, checkInside: false },
        { nodeType: Audio.name, checkInside: false },
      ],
      navigationNodeConfigs: [
        // 支持导航的节点类型
        { nodeType: CodeBlock.name },
        { nodeType: Mermaid.name },
      ],
    }),
    FileHandler.configure({
      allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
      onDrop: async (editor, files, pos) => {
        uploadImages(editor, files, pos);
      },
      onPaste: (editor, files) => {
        uploadImages(editor, files, editor.state.selection.from);
      },
    }),
    KeyDownCustomExtension.configure({
      Backspace: options?.keyDownOptions?.Backspace,
    }),
    Placeholder.configure({
      placeholder: 'Write thoughts here…',
      ...options?.placeholderOptions,
    }),
  ];
};
