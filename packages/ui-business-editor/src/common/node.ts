import {
  AudioExtension,
  Blockquote,
  BulletList,
  <PERSON><PERSON>lock,
  Diff<PERSON>lock,
  Divider,
  Document,
  HardBreak,
  Heading,
  Image,
  ListItem,
  Mathematics,
  MermaidExtension,
  OrderedList,
  Paragraph,
  SelectionEnd,
  SelectionStart,
  SVG,
  Table,
  TableCell,
  TableHeader,
  TableRow,
  TaskItem,
  TaskList,
  Text,
  Youtube,
} from '../extension';
import type { EditorExtensionOptions } from './type';

// 编辑器的 node 节点
export const getNodeExtensionList = (options?: EditorExtensionOptions) => {
  return [
    AudioExtension,
    Blockquote,
    BulletList,
    CodeBlock,
    DiffBlock.configure(options?.diffBlockOptions),
    Divider,
    Document,
    HardBreak,
    Heading,
    Image.configure(options?.imageOptions),
    ListItem,
    Mathematics,
    MermaidExtension.configure(options?.mermaidOptions),
    OrderedList,
    Paragraph,
    SelectionEnd,
    SelectionStart,
    SVG,
    Table,
    TableCell,
    TableHeader,
    TableRow,
    TaskItem,
    TaskList,
    Text,
    Youtube,
  ];
};
