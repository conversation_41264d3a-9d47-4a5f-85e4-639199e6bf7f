import './component.css';

import { cn } from '@repo/ui/lib/utils';
import { NodeViewContent, type NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Check, CopyIcon, Search } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { CODEBLOCK_LANGUAGES, CodeBlockLanguages, languageMap } from './const';

export const CodeBlockComponent = (props: NodeViewProps) => {
  const { editor, node, updateAttributes } = props;
  const {
    attrs: { language: defaultLanguage },
  } = node;
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [copyStatus, setCopyStatus] = useState<'idle' | 'copied'>('idle');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const languageButtonRef = useRef<HTMLButtonElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const changeLanguage = useCallback(
    (language: string) => {
      updateAttributes({ language });
      setIsLanguageDropdownOpen(false);
      setSearchQuery(''); // 重置搜索
      setSelectedIndex(0); // 重置选中索引
    },
    [updateAttributes],
  );

  const handleCopy = useCallback(async () => {
    await navigator.clipboard.writeText(node.textContent || '').then(() => {
      setCopyStatus('copied');
      setTimeout(() => setCopyStatus('idle'), 1000);
    });
  }, [node]);

  const currentLanguage =
    CODEBLOCK_LANGUAGES.find((lang) => lang.value === defaultLanguage) ||
    CodeBlockLanguages.plaintext;

  // 如果当前语言是 mermaid，则显示为 plaintext
  const displayLanguage =
    defaultLanguage === CodeBlockLanguages.mermaid.value
      ? CodeBlockLanguages.plaintext
      : currentLanguage;

  // 检查搜索查询是否匹配语言（包括别名）
  const matchesLanguage = useCallback((lang: { label: string; value: string }, query: string) => {
    const lowerQuery = query.toLowerCase();

    // 匹配 label 和 value
    if (
      lang.label.toLowerCase().includes(lowerQuery) ||
      lang.value.toLowerCase().includes(lowerQuery)
    ) {
      return true;
    }

    // 匹配别名
    for (const [alias, targetLang] of Object.entries(languageMap)) {
      if (alias.toLowerCase().includes(lowerQuery) && targetLang === lang.value) {
        return true;
      }
    }

    return false;
  }, []);

  // 过滤语言列表（包括别名搜索）
  const filteredLanguages = CODEBLOCK_LANGUAGES.filter(
    (lang) => lang.value !== CodeBlockLanguages.mermaid.value,
  ).filter((lang) => matchesLanguage(lang, searchQuery));

  // 计算下拉菜单位置
  const calculateDropdownPosition = useCallback(() => {
    if (languageButtonRef.current) {
      const rect = languageButtonRef.current.getBoundingClientRect();
      const scrollY = window.scrollY;
      const scrollX = window.scrollX;

      // 下拉菜单的最大高度 (与CSS中的max-h-[280px]保持一致)
      const dropdownMaxHeight = 280;
      const spacing = 4; // 按钮与下拉菜单之间的间距

      // 计算页面剩余高度
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;

      // 判断是否应该向上展开
      const shouldExpandUpward =
        spaceBelow < dropdownMaxHeight + spacing && spaceAbove > dropdownMaxHeight + spacing;

      setDropdownPosition({
        top: shouldExpandUpward
          ? rect.top + scrollY - dropdownMaxHeight - spacing
          : rect.bottom + scrollY + spacing,
        left: rect.left + scrollX,
      });
    }
  }, []);

  const handleDropdownToggle = useCallback(() => {
    if (!isLanguageDropdownOpen) {
      calculateDropdownPosition();
      setSearchQuery(''); // 打开时重置搜索
      setSelectedIndex(0); // 重置选中索引
    }
    setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
  }, [isLanguageDropdownOpen, calculateDropdownPosition]);

  // 处理键盘导航
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      // 阻止事件冒泡，避免触发编辑器快捷键
      e.stopPropagation();

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) => (prev < filteredLanguages.length - 1 ? prev + 1 : 0));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : filteredLanguages.length - 1));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredLanguages.length > 0) {
            changeLanguage(filteredLanguages[selectedIndex]?.value || '');
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsLanguageDropdownOpen(false);
          setSearchQuery('');
          setSelectedIndex(0);
          break;
      }
    },
    [filteredLanguages, selectedIndex, changeLanguage],
  );

  // 自动滚动到选中项
  const scrollToSelected = useCallback(() => {
    if (scrollContainerRef.current && selectedIndex >= 0) {
      const container = scrollContainerRef.current;
      const itemHeight = 32; // 每个选项的大概高度 (py-2 = 8px * 2 + text height)
      const containerHeight = container.clientHeight;
      const scrollTop = container.scrollTop;

      const selectedTop = selectedIndex * itemHeight;
      const selectedBottom = selectedTop + itemHeight;

      // 如果选中项在可视区域上方
      if (selectedTop < scrollTop) {
        container.scrollTop = selectedTop;
      }
      // 如果选中项在可视区域下方
      else if (selectedBottom > scrollTop + containerHeight) {
        container.scrollTop = selectedBottom - containerHeight;
      }
    }
  }, [selectedIndex]);

  // 当选中索引改变时自动滚动
  useEffect(() => {
    if (isLanguageDropdownOpen) {
      scrollToSelected();
    }
  }, [selectedIndex, isLanguageDropdownOpen, scrollToSelected]);

  // 当搜索结果变化时，重置选中索引
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchQuery]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        languageButtonRef.current &&
        !languageButtonRef.current.contains(event.target as Node)
      ) {
        setIsLanguageDropdownOpen(false);
        setSearchQuery(''); // 关闭时重置搜索
        setSelectedIndex(0); // 重置选中索引
      }
    };

    const handleScroll = () => {
      if (isLanguageDropdownOpen) {
        calculateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (isLanguageDropdownOpen) {
        calculateDropdownPosition();
      }
    };

    if (isLanguageDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isLanguageDropdownOpen, calculateDropdownPosition]);

  // 当下拉菜单打开时，聚焦搜索框
  useEffect(() => {
    if (isLanguageDropdownOpen && searchInputRef.current) {
      // 使用 setTimeout 确保 DOM 更新完成后再聚焦
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [isLanguageDropdownOpen]);

  const getEditEnabled = useCallback(() => {
    return editor.isEditable;
  }, [editor]);

  return (
    <NodeViewWrapper className="my-4">
      <div
        data-drag-handle
        className="youmind-code-block group relative rounded-lg border border-divider [.ProseMirror-selectednode_&]:!border-[#3b82f6] [.ProseMirror-selectednode_&]:bg-blue-50/10"
      >
        <div
          className={`flex items-center justify-between px-2 pb-1 pt-2 ${isLanguageDropdownOpen ? 'dropdown-open' : ''}`}
        >
          <div className="flex items-center">
            {getEditEnabled() ? (
              <div className="relative language-selector">
                <button
                  ref={languageButtonRef}
                  contentEditable={false}
                  className="flex cursor-pointer items-center gap-x-1 rounded-md px-2 py-1 text-xs text-[#6b7280] transition-all hover:bg-muted"
                  onClick={handleDropdownToggle}
                  type="button"
                >
                  <span>{displayLanguage.label}</span>
                  <svg
                    className={cn(
                      'chevron opacity-0 transition-all',
                      isLanguageDropdownOpen && 'rotate-180 opacity-100',
                      'group-hover:opacity-100',
                    )}
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M3 4.5L6 7.5L9 4.5"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            ) : (
              <div className="language-display">
                <span className="text-xs text-[#9ca3af]">{displayLanguage.label}</span>
              </div>
            )}
          </div>

          <div className="flex items-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
            {copyStatus === 'copied' ? (
              <Check
                className="cursor-pointer"
                color="#7bb88a"
                size={16}
                onClick={() => setCopyStatus('idle')}
              />
            ) : (
              <CopyIcon color="#9ca3af" className="cursor-pointer" size={14} onClick={handleCopy} />
            )}
          </div>
        </div>

        {/* 使用 Portal 渲染下拉菜单 */}
        {isLanguageDropdownOpen &&
          createPortal(
            <div
              className="absolute z-1000"
              ref={dropdownRef}
              style={{
                top: dropdownPosition.top,
                left: dropdownPosition.left,
              }}
            >
              <div className="max-h-[280px] max-w-[180px] overflow-y-auto rounded-lg border border-divider bg-card shadow-sm">
                <div className="relative p-2">
                  <Search
                    className="pointer-events-none absolute left-4 top-1/2 z-10 -translate-y-1/2 text-[#6b7280]"
                    size={14}
                  />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search languages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="pt-1 pr-3 pb-1 pl-6 w-full text-xs rounded-sm border transition-all outline-none search-input border-divider placeholder:text-disabled-foreground"
                  />
                </div>

                <div
                  ref={scrollContainerRef}
                  className="max-h-[200px] overflow-y-auto overscroll-contain"
                  onWheel={(e) => {
                    // 阻止滚动事件向外传播
                    e.stopPropagation();
                  }}
                >
                  {filteredLanguages.length > 0 ? (
                    filteredLanguages.map((lang, index) => (
                      <button
                        key={lang.value}
                        className={cn(
                          'w-full cursor-pointer px-3 py-2 text-left text-xs transition-all hover:bg-muted hover:text-foreground',
                          {
                            'bg-muted text-foreground':
                              index === selectedIndex ||
                              lang.value === defaultLanguage ||
                              (defaultLanguage === CodeBlockLanguages.mermaid.value &&
                                lang.value === CodeBlockLanguages.plaintext.value),
                          },
                        )}
                        onClick={() => changeLanguage(lang.value)}
                        onMouseEnter={() => setSelectedIndex(index)}
                        type="button"
                      >
                        {lang.label}
                      </button>
                    ))
                  ) : (
                    <div className="px-3 py-4 text-xs text-center">No languages found</div>
                  )}
                </div>
              </div>
            </div>,
            document.body,
          )}

        <pre className="px-4 mb-3 rounded-md bg-background" spellCheck={false}>
          <NodeViewContent as="code" spellCheck={false} />
        </pre>
      </div>
    </NodeViewWrapper>
  );
};
