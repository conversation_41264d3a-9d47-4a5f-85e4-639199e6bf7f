import { CodeBlock as CodeBlockBase } from '@repo/editor-common';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { all, createLowlight } from 'lowlight';
import { CodeBlockComponent } from './code-block-component';
import { registerLanguages } from './const';
import { LowlightPlugin } from './lowlight-plugin';

const lowlight = createLowlight(all);

registerLanguages(lowlight);

export const CodeBlock = CodeBlockBase.extend({
  addProseMirrorPlugins() {
    return [
      ...(this.parent?.() || []),
      LowlightPlugin({
        name: this.name,
        lowlight: this.options.lowlight,
        defaultLanguage: this.options.defaultLanguage,
      }),
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(CodeBlockComponent);
  },

  addKeyboardShortcuts() {
    return {
      ...(this.parent?.() || {}),
    };
  },
}).configure({
  lowlight: lowlight,
});
