import { Extension } from '@tiptap/core';
import { Plug<PERSON>, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

export const SelectionDisplayName = 'SelectionDisplay';

export const SelectionDisplay = Extension.create({
  name: SelectionDisplayName,
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('selection'),
        props: {
          decorations(state) {
            if (state.selection.empty) {
              return null;
            }

            return DecorationSet.create(state.doc, [
              Decoration.inline(state.selection.from, state.selection.to, {
                class: 'bg-muted',
              }),
            ]);
          },
        },
      }),
    ];
  },
});
