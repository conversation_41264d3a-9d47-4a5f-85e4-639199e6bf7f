import './style.css';
import { Extension } from '@tiptap/core';
import { EXTENSION_NAME } from './constant';
import { genSuggestion, type SlashCommandOptions } from './gen-suggestion';

export const SlashCommand = Extension.create<SlashCommandOptions>({
  name: EXTENSION_NAME,

  priority: 200,

  addOptions() {
    return {
      decorationClass: 'youmind-editor-slash-decoration',
    };
  },

  addProseMirrorPlugins(this) {
    return [genSuggestion(this.editor, this.options)];
  },

  addStorage() {
    return {
      rect: {
        width: 0,
        height: 0,
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
      },
    };
  },
});
