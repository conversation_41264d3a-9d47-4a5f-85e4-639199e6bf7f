// 斜杠命令的别名定义
export const COMMAND_ALIASES: Record<string, string[]> = {
  text: ['text', 'paragraph', 'p', 'text1', 'text2'],
  heading1: [
    'h1',
    '标题1',
    '一级标题',
    '大标题',
    'h',
    'title',
    'heading',
    '主标题',
    '首级标题',
    'title1',
    '头部',
    '章节',
    'section',
    'header',
    '主题',
    'topic',
    '大号',
  ],
  heading2: [
    'h2',
    '标题2',
    '二级标题',
    '中标题',
    'subtitle',
    '副标题',
    '次级标题',
    'heading2',
    'title2',
    '中等标题',
    '子章节',
    'subsection',
    '二级',
    '中号',
    'medium',
    '次标',
  ],
  heading3: [
    'h3',
    '标题3',
    '三级标题',
    '小标题',
    'subheading',
    '子标题',
    '三级',
    'heading3',
    'title3',
    '次标题',
    '小号',
    '分标题',
    'small',
    '子项',
    'subsub',
    '细分',
  ],
  taskList: [
    'todo',
    '任务列表',
    '待办',
    '清单',
    'checklist',
    'checkbox',
    'check',
    '任务',
    '勾选',
    '待办事项',
    'todo-list',
    'task',
    '计划',
    'plan',
    '完成',
    'done',
    '工作',
    'work',
    '复选',
    'tick',
  ],
  bulletList: [
    'ul',
    '无序列表',
    '项目列表',
    '列表',
    'bullets',
    'bullet',
    '点',
    '圆点',
    '无序',
    'unordered',
    'list',
    '项目',
    '条目',
    'item',
    'items',
    '要点',
    'points',
    '•',
    '点列表',
  ],
  orderedList: [
    'ol',
    '有序列表',
    '数字列表',
    '编号列表',
    'numbered',
    'numbers',
    '有序',
    '序号',
    'ordered',
    'number',
    '1',
    '123',
    '步骤',
    'steps',
    '流程',
    'process',
    '顺序',
    'sequence',
    '计数',
    'count',
  ],
  blockquote: [
    '引用',
    '引文',
    '块引用',
    'blockquote',
    'citation',
    'blockquote',
    'cite',
    'quote',
    '引号',
    '摘录',
    '>',
    '参考',
    'reference',
    '说明',
    'note',
    '备注',
    'remark',
    '说话',
    'speak',
  ],
  horizontalRule: [
    'hr',
    '分割线',
    '水平线',
    '分隔符',
    'separator',
    'line',
    'break',
    '横线',
    '间隔',
    'divider',
    '---',
    '-',
    '分界',
    'border',
    '切分',
    'split',
    '断行',
    '换段',
    '段落',
    '划线',
  ],
  image: [
    'img',
    'image',
    '图片',
    '图像',
    '照片',
    'pic',
    'picture',
    'photo',
    '图',
    '插图',
    '媒体',
    'media',
    '上传',
    'upload',
    '文件',
    'file',
    '素材',
    'asset',
    '视觉',
    'visual',
    '截图',
    'screenshot',
  ],
  codeBlock: [
    'code',
    'codeblock',
    'code-block',
    'js',
    'javascript',
    'ts',
    'typescript',
    '代码块',
    '代码',
    '编程',
    'snippet',
    'programming',
    '脚本',
    '程序',
    'script',
    '源码',
    '开发',
    'develop',
    '算法',
    'algorithm',
    '函数',
    'function',
    '方法',
    'method',
    '语法',
    'syntax',
  ],
  table: [
    'table',
    'grid',
    '表格',
    '表',
    '数据表',
    'spreadsheet',
    'excel',
    '数据',
    '表单',
    'rows',
    'columns',
    'sheet',
    '矩阵',
    'matrix',
    '统计',
    'stats',
    '报表',
    'report',
    '清单表',
    'database',
    'db',
    '统计表',
  ],
  mermaid: [
    'mermaid',
    '图表',
    '流程图',
    'diagram',
    'flowchart',
    'chart',
    '流程',
    '图形',
    '可视化',
    'visualization',
    '示意图',
    '架构图',
    'architecture',
    '时序图',
    'sequence',
    '甘特图',
    'gantt',
    '思维导图',
    'mindmap',
    '关系图',
    'graph',
    '网络图',
    'network',
  ],
};
