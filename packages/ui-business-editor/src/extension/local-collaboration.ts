import { Extension } from '@tiptap/core';
import { applyUpdate, Doc } from 'yjs';

export interface LocalCollaborationOptions {
  document?: Doc;
  field?: string;
  channelName?: string;
  enable?: boolean;
}

export const LocalCollaborationName = 'localCollaboration';

export const LocalCollaboration = Extension.create<LocalCollaborationOptions>({
  name: LocalCollaborationName,

  addOptions() {
    return {
      document: new Doc(),
      field: 'content',
      channelName: 'youmind-thought-editor-local-collaboration',
      enable: false,
    };
  },

  onCreate() {
    if (!this.options.enable) {
      return;
    }
    // 创建一个 BroadcastChannel 实例
    const channel = new BroadcastChannel(this.options.channelName || '');

    // 监听来自其他标签页的更新
    channel.addEventListener('message', (event) => {
      if (event.data.type === 'update' && event.data.docId === this.options.field) {
        // 应用从其他标签页接收到的更新
        applyUpdate(this.options.document || new Doc(), new Uint8Array(event.data.update));
      }
    });

    // 监听本地文档的更改并广播
    this.options.document?.on('update', (update: Uint8Array) => {
      // 将更新广播到其他标签页
      channel.postMessage({
        type: 'update',
        docId: this.options.field,
        update: Array.from(update), // 转换为普通数组以便可以被序列化
      });
    });

    // 当组件销毁时关闭通道
    this.editor.on('destroy', () => {
      channel.close();
    });
  },
});
