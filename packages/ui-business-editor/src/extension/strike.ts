import './strike.css';
import { Strike as StrikeBase } from '@repo/editor-common';
import { mergeAttributes } from '@tiptap/core';

export const Strike = StrikeBase.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {
        class: 'youmind-editor-mark-strike-ui',
      },
    };
  },

  renderHTML({ HTMLAttributes }) {
    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  },
});
