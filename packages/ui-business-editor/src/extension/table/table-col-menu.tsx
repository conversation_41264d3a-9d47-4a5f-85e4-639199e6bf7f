import { Button } from '@repo/ui/components/ui/button';
import { BubbleMenu } from '@tiptap/react';
import { ArrowLeftToLine, ArrowRightToLine, Trash } from 'lucide-react';
import { memo, useCallback } from 'react';

import type { MenuProps, ShouldShowProps } from './type';
import { getTableColumnsCount, isColumnGripSelected } from './utils';

export const TableColMenu = memo(({ editor, appendTo }: MenuProps) => {
  const shouldShow = useCallback(
    ({ view, state, from }: ShouldShowProps) => {
      if (!state) {
        return false;
      }

      return isColumnGripSelected({ editor, view, state, from: from || 0 });
    },
    [editor],
  );

  const onAddColumnBefore = useCallback(() => {
    editor.chain().focus().addColumnBefore().run();
  }, [editor]);

  const onAddColumnAfter = useCallback(() => {
    editor.chain().focus().addColumnAfter().run();
  }, [editor]);

  const onDeleteColumn = useCallback(() => {
    const columnsCount = getTableColumnsCount(editor.state.selection);
    if (columnsCount === 1) {
      // 如果只有一列，删除整个表格
      editor.chain().focus().deleteTable().run();
    } else {
      // 否则只删除当前列
      editor.chain().focus().deleteColumn().run();
    }
  }, [editor]);

  return (
    <BubbleMenu
      editor={editor}
      pluginKey="tableColumnMenu"
      shouldShow={shouldShow}
      updateDelay={0}
      tippyOptions={{
        appendTo: appendTo.current!,
        offset: [0, 5],
        popperOptions: {
          modifiers: [{ name: 'flip', enabled: false }],
        },
      }}
    >
      <div className="inline-flex flex-col gap-0.5 rounded-lg bg-white p-1.5 shadow-lg">
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddColumnBefore}
          variant="ghost"
        >
          <ArrowLeftToLine className="mr-2" size={16} />
          Add column before
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddColumnAfter}
          variant="ghost"
        >
          <ArrowRightToLine className="mr-2" size={16} />
          Add column after
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onDeleteColumn}
          variant="ghost"
        >
          <Trash className="mr-2" size={16} />
          Delete column
        </Button>
      </div>
    </BubbleMenu>
  );
});

TableColMenu.displayName = 'TableColMenu';
