import { type AudioAttributes, DIFF_CHANGE_ATTR_NAME, DIFF_CHANGE_TYPE } from '@repo/editor-common';
import { SimpleAudioPlayer } from '@repo/ui/components/custom/simple-audio-player';
import { ButtonWithTooltip } from '@repo/ui/components/ui/button';
import { cn } from '@repo/ui/lib/utils';
import { type NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Download, Edit2 } from 'lucide-react';
import { useState } from 'react';
import './index.css';

export const AudioComponent: React.FC<NodeViewProps> = (props: NodeViewProps) => {
  const { node, updateAttributes } = props;
  const { attrs } = node;
  const { src, alt } = attrs as AudioAttributes;

  let title = '';
  let album = '';
  const decodedAlt = decodeURIComponent(alt);
  try {
    const parsedAlt = JSON.parse(decodedAlt);
    title = parsedAlt.title;
    album = parsedAlt.album;
  } catch (err) {
    console.log(err);
  }

  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);

  const [isDownloading, setIsDownloading] = useState(false);
  const handleDownloadAudio = () => {
    setIsDownloading(true);
    fetch(src)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = blobUrl;
        a.target = '_blank';
        a.download = `${title}.mp3`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(blobUrl);
        setIsDownloading(false);
      });
  };

  const handleTitleChange = async (title: string) => {
    setIsEditingTitle(false);
    setLocalTitle(title);
    updateAttributes({
      alt: encodeURIComponent(
        JSON.stringify({
          title,
          album,
        }),
      ),
    });
  };

  return (
    <NodeViewWrapper
      className={cn(
        'group relative flex w-full items-center justify-center rounded-xl border border-muted shadow-sm',
        props.node.attrs[DIFF_CHANGE_ATTR_NAME] === DIFF_CHANGE_TYPE.REMOVED ? 'opacity-50' : '',
      )}
    >
      <SimpleAudioPlayer
        className="w-full h-full"
        track={{
          src,
          title: localTitle,
          artist: '',
          album,
        }}
        isEditingTitle={isEditingTitle}
        onTitleChange={handleTitleChange}
      />
      <div className="absolute hidden right-4 top-8 bg-card group-hover:flex gap-x-1">
        <ButtonWithTooltip
          disabled={isDownloading}
          tooltip={'Edit'}
          variant="icon"
          size="default"
          className="text-muted-foreground"
          onClick={() => setIsEditingTitle(true)}
        >
          <Edit2 />
        </ButtonWithTooltip>

        <ButtonWithTooltip
          disabled={isDownloading}
          tooltip={'Download'}
          variant="icon"
          size="default"
          className="text-muted-foreground"
          onClick={handleDownloadAudio}
        >
          <Download />
        </ButtonWithTooltip>
      </div>
    </NodeViewWrapper>
  );
};
