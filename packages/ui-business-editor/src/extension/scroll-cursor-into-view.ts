import { Extension } from '@tiptap/core';
import { findViewContainer } from './utils';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    scrollCursorIntoView: {
      scrollCursorIntoView: (offset?: number) => ReturnType;
    };
  }
}

export interface AutoScrollOptions {
  bottomOffset: number;
}

export const ScrollCursorIntoViewName = 'scrollCursorIntoView';

export const ScrollCursorIntoView = Extension.create<AutoScrollOptions>({
  name: ScrollCursorIntoViewName,

  addOptions() {
    return {
      bottomOffset: 150,
    };
  },

  addCommands() {
    return {
      scrollCursorIntoView:
        (offset?: number) =>
        ({ editor }) => {
          const { view } = editor;
          const { state } = view;
          const { selection } = state;
          const { $head } = selection;

          if (!$head) return false;

          const pos = view.coordsAtPos(selection.head);
          const editorElement = view.dom as HTMLElement;
          const viewContainer = findViewContainer(editorElement);

          if (!viewContainer) return false;

          // 确保容器是可滚动的
          const containerStyle = window.getComputedStyle(viewContainer);
          if (containerStyle.overflow === 'hidden' || containerStyle.overflowY === 'hidden') {
            return false;
          }

          const containerRect = viewContainer.getBoundingClientRect();
          const bottomOffset = offset ?? this.options.bottomOffset;

          const distanceToBottom = containerRect.bottom - pos.bottom;

          if (distanceToBottom < bottomOffset) {
            // 计算实际需要滚动的距离
            const scrollAmount = bottomOffset - distanceToBottom;

            // 使用 scrollIntoView 作为备选方案
            if (!viewContainer.scrollBy) {
              const targetElement = view.domAtPos(selection.head).node as HTMLElement;
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
              });
              return true;
            }

            // 使用 scrollBy 滚动
            requestAnimationFrame(() => {
              viewContainer.scrollBy({
                top: scrollAmount,
                behavior: 'smooth',
              });
            });
          }
          return true;
        },
    };
  },
});
