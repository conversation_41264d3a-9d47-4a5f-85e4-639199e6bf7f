export function findViewContainer(element: HTMLElement): HTMLElement | null {
  // 只在浏览器环境中执行
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return null;
  }

  let parent = element.parentElement;
  while (parent) {
    const { overflow, overflowY } = window.getComputedStyle(parent);
    if (
      overflow === 'auto' ||
      overflow === 'scroll' ||
      overflowY === 'auto' ||
      overflowY === 'scroll' ||
      parent.classList.contains('tiptap') // 检查是否是 tiptap 编辑器容器
    ) {
      return parent;
    }
    parent = parent.parentElement;
  }
  return (document.scrollingElement || document.documentElement) as HTMLElement;
}
