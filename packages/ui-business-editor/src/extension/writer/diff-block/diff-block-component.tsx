import './diff-block-component.css';
import {
  DIFF_ACTION,
  DiffBlockIdAttributeName,
  DiffBlockUpdateMetaKey,
  DiffTransformUtils,
  markdownSerializer,
  pmFragmentToNode,
  type ReportWriterDataParams,
} from '@repo/editor-common';
import { NodeViewContent, type NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Check, X } from 'lucide-react';
import type React from 'react';
import { useCallback, useRef } from 'react';
import { getDiffBlockManage } from './diff-block-manage';
import { getDiffBlockNodes } from './utils';

export const DiffBlockComponent: React.FC<NodeViewProps> = (props: NodeViewProps) => {
  const { getPos, node, editor } = props;
  const { attrs } = node;
  const contentRef = useRef<HTMLDivElement>(null);
  const hoverTimerRef = useRef<NodeJS.Timeout | null>(null);
  const diffTransformUtils = new DiffTransformUtils();

  const reportWriterData = useCallback(
    (params: Omit<ReportWriterDataParams, 'thought_id'>) => {
      // todo
      // const workflow = getWorkflow(editor);
      // const thoughtId = workflow?.id;
      // if (!thoughtId) {
      //   return;
      // }
      // diffTransformUtils.reportWriterData({
      //   thought_id: thoughtId,
      //   ...params,
      // });
    },
    [editor],
  );

  const handleDiffChanges = (props: NodeViewProps, action: DIFF_ACTION) => {
    const { editor, node, getPos } = props;
    if (!editor || typeof getPos !== 'function') {
      return;
    }

    const acceptContent = diffTransformUtils.cleanDiffBlock(node, DIFF_ACTION.ACCEPT);
    const rejectContent = diffTransformUtils.cleanDiffBlock(node, DIFF_ACTION.REJECT);
    const newContent = markdownSerializer.serialize(pmFragmentToNode(acceptContent));
    const oldContent = markdownSerializer.serialize(pmFragmentToNode(rejectContent));

    reportWriterData({
      action,
      nodes: [
        {
          id: node.attrs.diffBlockId,
          new: newContent,
          old: oldContent,
        },
      ],
    });

    const cleanedContent = action === DIFF_ACTION.ACCEPT ? acceptContent : rejectContent;

    const pos = getPos();
    const nodeSize = node.nodeSize;

    const allDiffBlockNodes = getDiffBlockNodes(editor);

    const curDiffBlockIndex = allDiffBlockNodes.findIndex(
      (node) => node.attrs.diffBlockId === attrs.diffBlockId,
    );

    editor.view.dispatch(
      editor.view.state.tr
        .deleteRange(pos, pos + nodeSize)
        .insert(pos, cleanedContent)
        .setMeta(DiffBlockUpdateMetaKey, {
          diffBlockId: attrs.diffBlockId,
          action,
          index: curDiffBlockIndex,
        }),
    );
  };

  const handleReject = (props: NodeViewProps) => {
    handleDiffChanges(props, DIFF_ACTION.REJECT);
  };

  const handleAccept = (props: NodeViewProps) => {
    handleDiffChanges(props, DIFF_ACTION.ACCEPT);
  };

  const renderOperationButtonList = () => {
    return (
      <div className="diff-operation-buttons flex h-[1.5rem] w-[3.625rem] items-center justify-center gap-x-2 rounded-full bg-brand-hover px-1 py-2">
        <div
          className="flex justify-center items-center cursor-pointer"
          onClick={() => handleAccept(props)}
        >
          <Check size={14} color="hsl(var(--card))" />
        </div>
        <div
          className="flex justify-center items-center cursor-pointer"
          onClick={() => handleReject(props)}
        >
          <X size={14} color="hsl(var(--card))" />
        </div>
      </div>
    );
  };

  const handleMouseEnter = () => {
    // 清除之前的定时器
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
    }

    // 设置新的定时器，500ms 后执行
    hoverTimerRef.current = setTimeout(() => {
      const diffManage = getDiffBlockManage(props.editor);
      if (diffManage) {
        diffManage.onHoverDiffBlock({
          diffBlockId: attrs.diffBlockId,
          pos: getPos(),
        });
      }
    }, 100);
  };

  const handleMouseLeave = () => {
    // 鼠标离开时清除定时器
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
      hoverTimerRef.current = null;
    }
  };

  return (
    <NodeViewWrapper
      {...{ [DiffBlockIdAttributeName]: attrs.diffBlockId }}
      contentEditable={false}
      className="diff-block-wrapper"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="pointer-events-none" ref={contentRef}>
        <NodeViewContent />
      </div>
      {renderOperationButtonList()}
    </NodeViewWrapper>
  );
};
