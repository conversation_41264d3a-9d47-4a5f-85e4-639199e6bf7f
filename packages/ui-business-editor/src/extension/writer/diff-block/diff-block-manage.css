.diff-block-manage-toolbar-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  .diff-block-manage-toolbar {
    display: flex;
    padding: 0.5rem 1rem 0.5rem 0.75rem;
    align-items: center;
    justify-content: space-between;
    border-radius: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.24);
    background: rgba(0, 0, 0, 0.6);
    box-shadow: 0px 2px 8px 0px rgba(2, 4, 26, 0.06);
    color: rgba(255, 255, 255, 0.88);
    font-size: 0.75rem;

    .toolbar-arrow-enable {
      color: hsla(0, 0%, 100%, 0.64);
    }

    .toolbar-arrow-disable {
      color: hsla(0, 0%, 100%, 0.24);
      pointer-events: none;
    }
  }
}

.editor-diff-block-manage-block-focus {
  box-shadow: 0 0 0 0.5rem hsl(var(--card-snips));
  background-color: hsl(var(--card-snips));

  .diff-operation-buttons {
    opacity: 1;
    visibility: visible;
  }
}
