import {
  DIFF_ACTION,
  DiffTransformUtils,
  markdownSerializer,
  pmFragmentToNode,
  type ReportWriterDataParams,
} from '@repo/editor-common';
import { cn } from '@repo/ui/lib/utils';
import type { Node } from '@tiptap/pm/model';
import type { Editor } from '@tiptap/react';
import { Check, ChevronDown, ChevronUp, X } from 'lucide-react';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { IDiffBlockManage } from './diff-block-manage';
import type { DiffBlockUpdateMeta } from './type';
import {
  type DiffBlockNode,
  focusDiffBlockNode,
  focusDiffBlockNodeWithoutScroll,
  getDiffBlockNodes,
} from './utils';

export enum DiffBlockManageToolbarStatus {
  DIFF = 'diff',
  SHOW_RESULT = 'showResult',
}

export interface DiffBlockManageToolbarProps {
  editor: Editor;
  diffBlockManage: IDiffBlockManage;
  initDiffBlockList: DiffBlockNode[];
}

export interface HoverDiffBlockParams {
  diffBlockId: string;
  pos: number;
}

export interface DiffBlockManageToolbarRef {
  updateDiffInfo: (meta?: DiffBlockUpdateMeta) => void;
  reRenderDiffInfo: () => void;
  onHoverDiffBlock: (params: HoverDiffBlockParams) => void;
}

const FOCUS_CLASS_NAME = 'editor-diff-block-manage-block-focus';

export const DiffBlockManageToolbar = forwardRef<
  DiffBlockManageToolbarRef,
  DiffBlockManageToolbarProps
>(({ editor, diffBlockManage, initDiffBlockList }, ref) => {
  const [status, setStatus] = useState<DiffBlockManageToolbarStatus>(
    DiffBlockManageToolbarStatus.DIFF,
  );
  const [curNode, setCurNode] = useState<DiffBlockNode | null>(initDiffBlockList[0] || null);
  const [curIndex, setCurIndex] = useState<number>(0);
  const [total, setTotal] = useState<number>(initDiffBlockList.length);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const diffTransformUtils = new DiffTransformUtils();

  useEffect(() => {
    if (status === DiffBlockManageToolbarStatus.SHOW_RESULT) {
      closeTimeoutRef.current = setTimeout(() => {
        diffBlockManage.hideManageToolbar();
      }, 1500);
    }
  }, [status, diffBlockManage]);

  const updateDiffInfo = useCallback(
    (meta?: DiffBlockUpdateMeta) => {
      const { index: updateDiffBlockIndex } = meta ?? {};

      const pendingNodeList = getDiffBlockNodes(editor);

      if (pendingNodeList.length === 0) {
        setStatus(DiffBlockManageToolbarStatus.SHOW_RESULT);
        return;
      }

      let newCurNode = curNode;

      setStatus(DiffBlockManageToolbarStatus.DIFF);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }

      if (updateDiffBlockIndex !== undefined) {
        if (updateDiffBlockIndex !== undefined) {
          const nextIndex =
            updateDiffBlockIndex < pendingNodeList.length
              ? updateDiffBlockIndex
              : updateDiffBlockIndex - 1 >= 0
                ? updateDiffBlockIndex - 1
                : 0;

          newCurNode = pendingNodeList[nextIndex] || pendingNodeList[0] || null;
        }
      }

      if (newCurNode) {
        setCurNode(newCurNode);
        focusDiffBlockNode(editor, newCurNode.attrs.diffBlockId, FOCUS_CLASS_NAME);

        const index = pendingNodeList.findIndex(
          (node: DiffBlockNode) => node.attrs.diffBlockId === newCurNode?.attrs.diffBlockId,
        );
        setCurIndex(index >= 0 ? index : 0);
      } else if (pendingNodeList.length > 0) {
        newCurNode = pendingNodeList[0] || null;
        if (newCurNode) {
          setCurNode(newCurNode);
          focusDiffBlockNode(editor, newCurNode.attrs.diffBlockId, FOCUS_CLASS_NAME);
          setCurIndex(0);
        }
      }

      setTotal(pendingNodeList.length);
      if (pendingNodeList.length === 0) {
        setStatus(DiffBlockManageToolbarStatus.SHOW_RESULT);
      }
    },
    [editor, diffBlockManage, curNode],
  );

  useEffect(() => {
    updateDiffInfo();
  }, []);

  const reRenderDiffInfo = useCallback(() => {
    const diffBlockNodes = getDiffBlockNodes(editor);
    const pendingNodes = diffBlockNodes;
    const firstNode = pendingNodes[0];

    if (pendingNodes.length > 0 && firstNode) {
      setCurNode(firstNode);
      focusDiffBlockNode(editor, firstNode?.attrs.diffBlockId, FOCUS_CLASS_NAME);
      setCurIndex(0);
      setTotal(pendingNodes.length);
      setStatus(DiffBlockManageToolbarStatus.DIFF);
    } else {
      setStatus(DiffBlockManageToolbarStatus.SHOW_RESULT);
    }
  }, [editor]);

  const onHoverDiffBlock = useCallback(
    ({ diffBlockId, pos }: HoverDiffBlockParams) => {
      const diffBlockNodes = getDiffBlockNodes(editor);
      const pendingNodes = diffBlockNodes;

      // 找到对应的节点
      const targetNode = pendingNodes.find(
        (node: DiffBlockNode) => node.attrs.diffBlockId === diffBlockId,
      );

      if (targetNode) {
        // 更新工具栏状态
        setCurNode(targetNode);

        // 添加焦点样式但不滚动
        focusDiffBlockNodeWithoutScroll(editor, diffBlockId, FOCUS_CLASS_NAME);

        // 更新当前索引
        const index = pendingNodes.findIndex(
          (node: DiffBlockNode) => node.attrs.diffBlockId === diffBlockId,
        );
        if (index >= 0) {
          setCurIndex(index);
        }
      }
    },
    [editor],
  );

  useImperativeHandle(ref, () => ({
    updateDiffInfo,
    reRenderDiffInfo,
    onHoverDiffBlock,
  }));

  const getReportData = (diffBlockNodeList: Node[]) => {
    const res = diffBlockNodeList.map((node) => {
      const { diffBlockId } = node.attrs;
      const acceptContent = diffTransformUtils.cleanDiffBlock(node, DIFF_ACTION.ACCEPT);
      const rejectContent = diffTransformUtils.cleanDiffBlock(node, DIFF_ACTION.REJECT);
      return {
        id: diffBlockId,
        new: markdownSerializer.serialize(pmFragmentToNode(acceptContent)),
        old: markdownSerializer.serialize(pmFragmentToNode(rejectContent)),
      };
    });
    return res;
  };

  const reportWriterData = useCallback(
    (params: Omit<ReportWriterDataParams, 'thought_id'>) => {
      // const workflow = getWorkflow(editor);
      // if (!workflow) {
      //   return;
      // }
      // const thoughtId = workflow?.id;
      // if (!thoughtId) {
      //   return;
      // }
      // const resolveVersion = {
      //   thought_title: workflow?.getCurTitle(),
      //   content: {
      //     plain: markdownSerializer.serialize(editor.state.doc),
      //     raw: editorContentUtils.yDocToBase64(workflow.getYDoc()),
      //   },
      // };
      // diffTransformUtils.reportWriterData({
      //   thought_id: thoughtId,
      //   resolve_version: resolveVersion,
      //   ...params,
      // });
    },
    [editor],
  );

  const processAllDiffs = useCallback(
    (action: DIFF_ACTION) => {
      const doc = editor.state.doc;

      const diffBlockNodes = getDiffBlockNodes(editor);

      const reportNodeData = getReportData(diffBlockNodes);

      const processedContent = diffTransformUtils.cleanDiffBlock(doc, action);

      const newDoc = doc.type.create(doc.attrs, processedContent);

      editor.commands.setContent(newDoc.toJSON());

      // Update UI state after editor content changes
      setTimeout(() => {
        updateDiffInfo();

        setTimeout(() => {
          reportWriterData({
            nodes: reportNodeData,
            action,
          });
        }, 3000);
      }, 50);
    },
    [editor, updateDiffInfo],
  );

  const updateData = useCallback(() => {
    // const workflow = getWorkflow(editor);
    // workflow?.boundHandleDataUpdate();
  }, [editor]);

  const acceptAll = useCallback(() => {
    processAllDiffs(DIFF_ACTION.ACCEPT);
    setTimeout(() => {
      updateData();
    }, 800);
  }, [processAllDiffs]);

  const rejectAll = useCallback(() => {
    processAllDiffs(DIFF_ACTION.REJECT);
    setTimeout(() => {
      updateData();
    }, 800);
  }, [processAllDiffs]);

  const onClickPrev = useCallback(() => {
    const diffBlockNodes = getDiffBlockNodes(editor);
    const pendingNodes = diffBlockNodes;

    const prevIndex = (curIndex - 1 + total) % total;
    const prevNode = pendingNodes[prevIndex];

    if (prevNode) {
      setCurNode(prevNode);
      focusDiffBlockNode(editor, prevNode.attrs.diffBlockId, FOCUS_CLASS_NAME);
      setCurIndex(prevIndex);
    }
  }, [curIndex, total, editor]);

  const onClickNext = useCallback(() => {
    const diffBlockNodes = getDiffBlockNodes(editor);
    const pendingNodes = diffBlockNodes;

    const nextIndex = (curIndex + 1) % total;
    const nextNode = pendingNodes[nextIndex];

    if (nextNode) {
      setCurNode(nextNode);
      focusDiffBlockNode(editor, nextNode.attrs.diffBlockId, FOCUS_CLASS_NAME);
      setCurIndex(nextIndex);
    }
  }, [curIndex, total, editor]);

  const renderDiffInfo = () => {
    const diffBlockNodes = getDiffBlockNodes(editor);
    const pendingNodes = diffBlockNodes;
    const hasMultipleNodes = pendingNodes.length > 1;

    return (
      <>
        <ChevronDown
          onClick={onClickNext}
          className={cn(
            hasMultipleNodes ? 'toolbar-arrow-enable' : 'toolbar-arrow-disable',
            'cursor-pointer',
          )}
          size={16}
        />
        <div>
          {curIndex + 1} of {total}
        </div>
        <ChevronUp
          onClick={onClickPrev}
          className={cn(
            hasMultipleNodes ? 'toolbar-arrow-enable' : 'toolbar-arrow-disable',
            'cursor-pointer',
          )}
          size={16}
        />
      </>
    );
  };

  const renderResultInfo = () => {
    return <div className="w-full h-full">All diffs have been handled</div>;
  };

  const renderOperationButton = () => {
    return (
      <>
        <div className="flex gap-x-1 items-center cursor-pointer" onClick={rejectAll}>
          <X size={12} />
          <div>Reject all</div>
        </div>
        <div className="flex gap-x-1 items-center cursor-pointer" onClick={acceptAll}>
          <Check size={12} />
          <div>Accept all</div>
        </div>
      </>
    );
  };

  return (
    <div className="gap-x-10 text-sm diff-block-manage-toolbar">
      <div className="flex gap-x-1 items-center">
        {status === DiffBlockManageToolbarStatus.DIFF ? renderDiffInfo() : renderResultInfo()}
      </div>
      {status === DiffBlockManageToolbarStatus.DIFF && (
        <div className="flex gap-x-3 items-center">{renderOperationButton()}</div>
      )}
    </div>
  );
});

DiffBlockManageToolbar.displayName = 'DiffBlockManageToolbar';
