import { DiffBlockExtensionName } from '@repo/editor-common';
import type { Node as PMNode } from '@tiptap/pm/model';
import type { Editor } from '@tiptap/react';
export interface DiffBlockNode extends PMNode {
  attrs: {
    diffBlockId: string;
  };
}

export const getDiffBlockNodes = (editor: Editor): DiffBlockNode[] => {
  const diffBlockNodes: DiffBlockNode[] = [];

  editor.state.doc.descendants((node) => {
    if (node.type.name === DiffBlockExtensionName) {
      diffBlockNodes.push(node as DiffBlockNode);
    }
    return true;
  });

  return diffBlockNodes;
};

export const findDiffBlockNodeById = (
  diffBlockNodes: DiffBlockNode[],
  diffBlockId: string,
): DiffBlockNode | undefined => {
  return diffBlockNodes.find((node) => node.attrs.diffBlockId === diffBlockId);
};

export const findNextOrPrevDiffBlockNode = (
  diffBlockNodes: DiffBlockNode[],
  currentNodeId: string,
): DiffBlockNode | undefined => {
  const currentIndex = diffBlockNodes.findIndex((node) => node.attrs.diffBlockId === currentNodeId);

  if (currentIndex === -1) return;

  if (currentIndex + 1 < diffBlockNodes.length) {
    return diffBlockNodes[currentIndex + 1];
  }

  if (currentIndex - 1 >= 0) {
    return diffBlockNodes[currentIndex - 1];
  }

  return;
};

// 获取 diff block 节点的位置信息
const getDiffBlockNodesWithPos = (editor: Editor): Array<{ node: DiffBlockNode; pos: number }> => {
  const diffBlockNodesWithPos: Array<{ node: DiffBlockNode; pos: number }> = [];

  editor.state.doc.descendants((node, pos) => {
    if (node.type.name === DiffBlockExtensionName) {
      diffBlockNodesWithPos.push({
        node: node as DiffBlockNode,
        pos,
      });
    }
    return true;
  });

  return diffBlockNodesWithPos;
};

// 通过 pos 获取对应的 DOM 元素
const getDOMElementByPos = (
  editor: Editor,
  pos: number,
  diffBlockId: string,
): HTMLElement | null => {
  try {
    const domAtPos = editor.view.domAtPos(pos);
    if (!domAtPos.node) return null;

    let targetElement: HTMLElement | null = null;

    // 如果是元素节点，直接检查或在其中查找
    if (domAtPos.node.nodeType === Node.ELEMENT_NODE) {
      const element = domAtPos.node as HTMLElement;
      if (element.getAttribute?.('data-diff-block-id') === diffBlockId) {
        targetElement = element;
      } else {
        targetElement = element.querySelector(`[data-diff-block-id="${diffBlockId}"]`);
      }
    } else {
      // 如果是文本节点，向上查找父元素
      let parentElement = domAtPos.node.parentElement;
      while (parentElement && !targetElement) {
        if (parentElement.getAttribute?.('data-diff-block-id') === diffBlockId) {
          targetElement = parentElement;
          break;
        }
        targetElement = parentElement.querySelector(`[data-diff-block-id="${diffBlockId}"]`);
        if (targetElement) break;
        parentElement = parentElement.parentElement;
      }
    }

    return targetElement;
  } catch (error) {
    console.warn('Failed to get DOM element by pos:', error);
    return null;
  }
};

export const focusDiffBlockNode = (
  editor: Editor,
  diffBlockId: string,
  focusClassName: string,
): void => {
  // 获取所有 diff block 节点及其位置信息
  const diffBlockNodesWithPos = getDiffBlockNodesWithPos(editor);

  let targetElement: HTMLElement | null = null;

  // 遍历处理每个 diff block 节点
  diffBlockNodesWithPos.forEach(({ node, pos }) => {
    const nodeId = node.attrs.diffBlockId;
    const element = getDOMElementByPos(editor, pos, nodeId);

    if (element) {
      if (nodeId === diffBlockId) {
        // 目标元素，添加焦点样式
        if (!element.classList.contains(focusClassName)) {
          element.classList.add(focusClassName);
        }
        targetElement = element;
      } else {
        // 非目标元素，移除焦点样式
        if (element.classList.contains(focusClassName)) {
          element.classList.remove(focusClassName);
        }
      }
    }
  });

  // 处理滚动逻辑
  if (targetElement) {
    const scrollTargetElement = targetElement as HTMLElement;
    // Find the scrollable parent container
    const findScrollParent = (node: HTMLElement): HTMLElement => {
      if (!node || node === document.body) return document.body;

      const overflowY = window.getComputedStyle(node).overflowY;
      const isScrollable = overflowY !== 'visible' && overflowY !== 'hidden';

      if (isScrollable && node.scrollHeight > node.clientHeight) {
        return node;
      }

      return findScrollParent(node.parentElement as HTMLElement);
    };

    const scrollParent = findScrollParent(scrollTargetElement);

    const parentRect = scrollParent.getBoundingClientRect();
    const targetRect = scrollTargetElement.getBoundingClientRect();

    const isFullyVisible =
      targetRect.top >= parentRect.top && targetRect.bottom <= parentRect.bottom;

    if (!isFullyVisible) {
      if (scrollParent === document.body) {
        window.scrollTo({
          top: window.pageYOffset + targetRect.top - window.innerHeight / 2,
          behavior: 'smooth',
        });
      } else {
        scrollTargetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }
};

// 只添加焦点样式，不滚动的版本
export const focusDiffBlockNodeWithoutScroll = (
  editor: Editor,
  diffBlockId: string,
  focusClassName: string,
): void => {
  // 获取所有 diff block 节点及其位置信息
  const diffBlockNodesWithPos = getDiffBlockNodesWithPos(editor);

  // 遍历处理每个 diff block 节点
  diffBlockNodesWithPos.forEach(({ node, pos }) => {
    const nodeId = node.attrs.diffBlockId;
    const element = getDOMElementByPos(editor, pos, nodeId);

    if (element) {
      if (nodeId === diffBlockId) {
        // 目标元素，添加焦点样式
        if (!element.classList.contains(focusClassName)) {
          element.classList.add(focusClassName);
        }
      } else {
        // 非目标元素，移除焦点样式
        if (element.classList.contains(focusClassName)) {
          element.classList.remove(focusClassName);
        }
      }
    }
  });
};
