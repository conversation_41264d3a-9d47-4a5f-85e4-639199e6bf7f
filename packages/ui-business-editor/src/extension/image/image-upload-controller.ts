const callHTTP = async (...params: any[]) => {
  return {} as any;
};
const sha256File = async (file: File) => {
  return '';
};

export enum UploadImageStatus {
  UPLOADING = 'uploading',
  UPLOADED = 'uploaded',
  ERROR = 'error',
}

interface UploadImageRes {
  status: UploadImageStatus;
  url: string;
}

export class ImageUploadController {
  private async calculateSha(file: File) {
    return await sha256File(file);
  }

  private async uploadImageCall(hash: string, file: File) {
    const { data: upload_url, error } = await callHTTP('/api/v1/genSignedPutUrlIfNotExist', {
      method: 'POST',
      body: { hash },
    });
    if (error) {
      throw error;
    }
    if (upload_url) {
      const headers: { 'Content-Type'?: string } = {};
      if (file?.type) {
        headers['Content-Type'] = file.type;
      }
      const { error: uploadError } = await callHTTP(upload_url, {
        method: 'PUT',
        body: file,
        headers,
      });
      if (uploadError) {
        throw uploadError;
      }
    }
  }

  async extractImage(imageUrl: string) {
    let fullImageUrl = imageUrl;
    try {
      const url = new URL(imageUrl);
      fullImageUrl = url.toString();
    } catch {
      fullImageUrl = `${window.location.origin}${imageUrl}`;
    }

    try {
      const { data, error } = await callHTTP('/api/v1/extractText', {
        method: 'POST',
        body: { url: fullImageUrl, adaptImage: true },
        silent: true,
      });
      if (error) {
        return '';
      }
      return data.text;
    } catch {
      return '';
    }
  }

  async uploadImage(file: File): Promise<UploadImageRes> {
    try {
      const hash = await this.calculateSha(file);
      await this.uploadImageCall(hash, file);
      return {
        status: UploadImageStatus.UPLOADED,
        url: `/files/${hash}`,
      };
    } catch (error) {
      return {
        status: UploadImageStatus.ERROR,
        url: '',
      };
    }
  }
}
