import './image-node-view.css';
import { DIFF_CHANGE_ATTR_NAME, type ImageAttrs, ImageStatus } from '@repo/editor-common';
import { type NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { DisplayImageNode } from './display-image-node';
import { ImageFailedComponent } from './image-failed-component';
import { PlaceholderComponent } from './placeholder-component';
import { UploadingImageNode } from './uploading-image-node';
import { isBase64Image, isExternalImage } from './utils';

export const ImageNodeView = (props: NodeViewProps) => {
  const { node } = props;
  const { status, src } = node.attrs as ImageAttrs;

  const getComponent = () => {
    const isExternal = src && isExternalImage(src);
    const isBase64 = src && isBase64Image(src);
    // const isExternal = false;
    // const isBase64 = false;

    if (status === ImageStatus.FAILED || (!src && status !== ImageStatus.UPLOAD_IMAGE)) {
      return <ImageFailedComponent {...props} />;
    }
    if (status === ImageStatus.UPLOAD_IMAGE || isExternal || isBase64) {
      return (
        <UploadingImageNode
          {...props}
          isExternalImage={Boolean(isExternal)}
          isBase64Image={Boolean(isBase64)}
        />
      );
    }
    return <DisplayImageNode {...props} />;
  };

  return (
    <NodeViewWrapper
      // 下面这个属性是给 diff-block 组件用的
      {...{ [DIFF_CHANGE_ATTR_NAME]: node.attrs[DIFF_CHANGE_ATTR_NAME] }}
      className="bg-transparent"
    >
      <PlaceholderComponent nodeViewProps={props} position="top" />
      <div className="flex justify-center w-full view-node-image-container">{getComponent()}</div>
      <PlaceholderComponent nodeViewProps={props} position="bottom" />
    </NodeViewWrapper>
  );
};
