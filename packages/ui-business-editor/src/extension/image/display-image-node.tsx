import type { ImageAttrs, ImageExtensionOptions } from '@repo/editor-common';
import {
  DIFF_CHANGE_ATTR_NAME,
  DIFF_CHANGE_TYPE,
  ImageStatus,
  MAX_IMAGE_HEIGHT,
  MIN_IMAGE_WIDTH,
} from '@repo/editor-common';
import { ImageToolbar } from '@repo/ui/components/custom/image-toolbar';
import { cn } from '@repo/ui/lib/utils';
import type { NodeViewProps } from '@tiptap/react';
import { Image as AntdImage } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { calculateImageDisplaySize, getCurImageList } from './utils';

const { PreviewGroup } = AntdImage;

export const DisplayImageNode = (props: NodeViewProps) => {
  const { node, editor, extension, updateAttributes } = props;
  const imageOptions = extension.options as ImageExtensionOptions;
  const {
    width: imageDisplayWidth,
    height: imageDisplayHeight,
    id: imageId,
    src,
    alt: imageAlt,
  } = node.attrs as ImageAttrs;
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImageList, setPreviewImageList] = useState<
    Array<{ id: string; src: string; alt: string }>
  >([]);
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [loadingImage, setLoadingImage] = useState(
    !editor.storage.image.uploadingImageMap.has(imageId),
  );
  const imgRef = useRef<HTMLImageElement | null>(null);
  const [useFallbackUrl, setUseFallbackUrl] = useState(false);

  const doubleClickTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastClickTimeRef = useRef<number>(0);

  const { isDragging, dragSide, displayRatio, imageContainerRef, onDragImageSize } = useDrag({
    imageDisplayWidth,
    imageDisplayHeight,
    updateAttributes,
  });

  const getImageUrl = () => {
    if (editor.storage.image.uploadingImageMap.has(imageId)) {
      return editor.storage.image.uploadingImageMap.get(imageId)?.dataUrl;
    }

    // 如果图片 URL 以 /files 开头，转换为 CDN 格式
    if (src?.startsWith('/files/')) {
      const hash = src.replace('/files/', '');
      // 如果需要使用fallback URL，返回原始URL
      if (useFallbackUrl) {
        return src;
      }
      // 否则返回CDN格式的@large URL
      return `https://cdn.gooo.ai/user-files/${hash}@large`;
    }

    return src;
  };

  const renderLoading = () => {
    const hasImageData = editor.storage.image.uploadingImageMap.has(imageId);
    if (hasImageData) {
      return <></>;
    }
    if (loadingImage) {
      return (
        <div
          className={cn(
            'flex absolute flex-col gap-3 justify-center items-center w-full h-full bg-gray-100 rounded-lg',
          )}
        >
          <div className="dot-pulse" />
        </div>
      );
    }
  };

  const onMaximizeImage = () => {
    const imageList = getCurImageList(editor);
    const curImageIndex = imageList.findIndex((image) => image.id === node.attrs.id);

    if (curImageIndex === -1) {
      setPreviewImageList([
        {
          id: node.attrs.id,
          src: node.attrs.src,
          alt: node.attrs.alt,
        },
      ]);
      setPreviewImageIndex(0);
      setPreviewVisible(true);
      return;
    } else {
      setPreviewImageList(imageList);
      setPreviewImageIndex(curImageIndex);
      setPreviewVisible(true);
    }
  };

  // 处理点击事件，实现600ms间隔的双击
  const handleClick = () => {
    if (loadingImage || !imageOptions.maxviewEnable) {
      return;
    }
    const now = Date.now();
    const timeSinceLastClick = now - lastClickTimeRef.current;

    if (timeSinceLastClick < 600) {
      // 双击事件触发
      if (doubleClickTimeoutRef.current) {
        clearTimeout(doubleClickTimeoutRef.current);
        doubleClickTimeoutRef.current = null;
      }
      onMaximizeImage();
    } else {
      // 第一次点击，设置定时器
      lastClickTimeRef.current = now;
    }
  };

  const onLinkImage = async () => {
    try {
      const fullUrl = new URL(node.attrs.src, window.location.origin).toString();
      if (imageOptions.handleCopyImageUrl) {
        imageOptions.handleCopyImageUrl({ url: fullUrl, editor });
      }
    } catch (err) {
      console.error(err);
    }
  };

  const renderTools = () => {
    // 在 diff 模式下，不显示图片工具栏
    if (node.attrs[DIFF_CHANGE_ATTR_NAME] || loadingImage) {
      return null;
    }
    return (
      <>
        {editor.isEditable && imageOptions.resizeEnable && (
          <>
            {/* 左侧拖拽线 */}
            <div
              className="left-drag-line"
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onDragImageSize(true, e);
              }}
            />

            {/* 右侧拖拽线 */}
            <div
              className="right-drag-line"
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onDragImageSize(false, e);
              }}
            />
          </>
        )}

        {/* 图片工具栏 */}
        {src && (
          <ImageToolbar
            src={src}
            alt={imageAlt}
            type={'png'}
            viewImageEnabled={imageOptions.maxviewEnable}
            downloadEnabled={imageOptions.downloadEnable}
            copyLinkEnabled={imageOptions.copyLinkEnable}
            copyToClipboardEnabled={imageOptions.copyEnable}
            insertToThoughtEnabled={false}
            saveEnabled={false}
            onViewImage={onMaximizeImage}
            onCopyLink={onLinkImage}
            editEnabled={false}
          />
        )}
      </>
    );
  };

  return (
    <div className="relative flex justify-center w-full group">
      <div
        data-drag-handle
        ref={imageContainerRef}
        className={`node-image-container relative flex max-w-full items-center justify-center rounded-lg border-2 ${
          isDragging ? `dragging-${dragSide}` : ''
        }`}
        style={{
          width: imageDisplayWidth ? imageDisplayWidth : '100%',
          aspectRatio: displayRatio,
        }}
        onClick={handleClick}
      >
        <img
          loading="lazy"
          onLoad={() => {
            setLoadingImage(false);
            // 如果图片没有宽高，从加载后的图片获取宽高并回填
            if ((!imageDisplayWidth || !imageDisplayHeight) && imgRef.current) {
              const naturalWidth = imgRef.current.naturalWidth;
              const naturalHeight = imgRef.current.naturalHeight;

              // 添加尺寸验证和保障逻辑
              if (naturalWidth && naturalHeight && naturalWidth > 0 && naturalHeight > 0) {
                // 限制最大高度
                const size = calculateImageDisplaySize(naturalWidth, naturalHeight);

                updateAttributes({
                  width: size.imageDisplayWidth,
                  height: size.imageDisplayHeight,
                });
              } else {
                // 尺寸获取失败，将图片状态设置为失败
                updateAttributes({
                  status: ImageStatus.FAILED,
                });
              }
            }
          }}
          onError={() => {
            // 如果是@large版本加载失败，尝试加载原始图片
            if (!useFallbackUrl && src && src.startsWith('/files/')) {
              setUseFallbackUrl(true);
              setLoadingImage(true); // 重新开始加载原始URL
            } else {
              // 如果原始图片也加载失败，设置为失败状态
              setLoadingImage(false);
              updateAttributes({
                status: ImageStatus.FAILED,
              });
            }
          }}
          ref={imgRef}
          src={getImageUrl()}
          alt={imageAlt}
          className={cn(
            'h-full w-full rounded-md object-cover',
            node.attrs[DIFF_CHANGE_ATTR_NAME] === DIFF_CHANGE_TYPE.REMOVED ? 'opacity-50' : '',
          )}
        />
        {renderLoading()}
        {renderTools()}
        {/* 图片预览组 */}
        <PreviewGroup
          items={previewImageList.map((image) => image.src)}
          preview={{
            onVisibleChange: (visible) => {
              setPreviewVisible(visible);
            },
            visible: previewVisible,
            current: previewImageIndex,
            onChange: (current) => {
              setPreviewImageIndex(current);
            },
          }}
        />
      </div>
    </div>
  );
};

export function useDrag({
  imageDisplayWidth,
  imageDisplayHeight,
  maxImageHeight,
  updateAttributes,
}: {
  imageDisplayWidth?: number;
  imageDisplayHeight?: number;
  maxImageHeight?: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updateAttributes: (attributes: any) => void;
}) {
  const [dragSide, setDragSide] = useState<'left' | 'right'>('right');
  const [isDragging, setIsDragging] = useState(false);
  const animationFrameRef = useRef<number | null>(null);
  const [initialWidth, setInitialWidth] = useState(0);
  const [initialX, setInitialX] = useState(0);
  const [displayRatio, setDisplayRatio] = useState(
    imageDisplayWidth && imageDisplayHeight ? imageDisplayWidth / imageDisplayHeight : 1,
  );
  const lastMousePosRef = useRef<number>(0);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const onDragImageSize = (isLeft: boolean, e: React.MouseEvent) => {
    if (!imageContainerRef.current) return;

    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    const currentWidth = imageContainerRef.current.offsetWidth;
    setInitialWidth(currentWidth);
    setInitialX(e.clientX);
    lastMousePosRef.current = e.clientX;
    setDragSide(isLeft ? 'left' : 'right');
    setIsDragging(true);

    e.preventDefault();
  };

  // 拖拽调整大小处理
  useEffect(() => {
    if (!isDragging) return;

    // 鼠标移动处理函数
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !imageContainerRef.current) return;

      lastMousePosRef.current = e.clientX;

      if (animationFrameRef.current === null) {
        animationFrameRef.current = requestAnimationFrame(() => {
          const deltaX = lastMousePosRef.current - initialX;
          let newWidth = initialWidth;

          if (dragSide === 'left') {
            newWidth = Math.max(MIN_IMAGE_WIDTH, initialWidth - deltaX);
          } else {
            newWidth = Math.max(MIN_IMAGE_WIDTH, initialWidth + deltaX);
          }

          if (imageContainerRef.current?.parentElement) {
            newWidth = Math.min(newWidth, imageContainerRef.current?.parentElement?.offsetWidth);
          }

          let newHeight = 0;

          newHeight = newWidth / displayRatio;

          if (imageContainerRef.current) {
            imageContainerRef.current.style.width = `${newWidth}px`;
            imageContainerRef.current.style.height = `${newHeight}px`;
            imageContainerRef.current.style.aspectRatio = displayRatio.toString();
          }

          animationFrameRef.current = null;
        });
      }

      e.preventDefault();
    };

    // 鼠标释放处理函数
    const handleMouseUp = () => {
      if (isDragging && imageContainerRef.current) {
        setIsDragging(false);

        if (animationFrameRef.current !== null) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }

        const newWidth = parseInt(imageContainerRef.current.style.width);
        let newHeight = 0;
        if (displayRatio) {
          newHeight = newWidth / displayRatio;
        }

        // 再次检查高度是否超过最大限制
        if (newHeight > (maxImageHeight ?? MAX_IMAGE_HEIGHT)) {
          newHeight = maxImageHeight ?? MAX_IMAGE_HEIGHT;
          const adjustedWidth = newHeight * displayRatio;
          updateAttributes({
            width: adjustedWidth,
            height: newHeight,
          });
        } else {
          updateAttributes({
            width: newWidth,
            height: newHeight,
          });
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove, {
      passive: false,
    });
    document.addEventListener('mouseup', handleMouseUp, { passive: true });

    // 清理函数
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [
    isDragging,
    initialWidth,
    initialX,
    dragSide,
    updateAttributes,
    displayRatio,
    imageDisplayWidth,
    imageDisplayHeight,
  ]);

  return {
    isDragging,
    dragSide,
    displayRatio,
    setDisplayRatio,
    imageContainerRef,
    onDragImageSize,
  };
}
