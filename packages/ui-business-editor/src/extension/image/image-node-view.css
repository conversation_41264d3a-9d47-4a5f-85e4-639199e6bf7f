.youmind-editor-focused {
  .ProseMirror-selectednode {
    .view-node-image-container {
      .image-uploading-view {
        border: 2px solid #3b82f6;
        position: relative;
      }
      .node-image-container {
        border: 2px solid #3b82f6;
        position: relative;
      }
    }
  }
}

.left-drag-line {
  position: absolute;
  height: 40px;
  max-height: 100%;
  padding: 1px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  border-radius: 3px;
  transition: background-color 150ms;
  opacity: 0;
  background-color: lch(10.633 1.867 272 / 0.8);
  -webkit-backdrop-filter: saturate(1.8) blur(20px);
  backdrop-filter: saturate(1.8) blur(20px);
  border: 0.5px solid rgba(255, 255, 255, 0.4);
  left: -20px;
  cursor: col-resize;
  &::before {
    position: absolute;
    content: "";
    inset: 0px -6px 0px -4px;
  }
}

.right-drag-line {
  display: flex;
  position: absolute;
  height: 40px;
  max-height: 100%;
  padding: 1px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  border-radius: 3px;
  transition: background-color 150ms;
  opacity: 0;
  background-color: lch(10.633 1.867 272 / 0.8);
  -webkit-backdrop-filter: saturate(1.8) blur(20px);
  backdrop-filter: saturate(1.8) blur(20px);
  border: 0.5px solid rgba(255, 255, 255, 0.4);
  right: -20px;
  cursor: col-resize;

  &::before {
    position: absolute;
    content: "";
    inset: 0px -6px 0px -4px;
  }
}

.image-preivew-tool {
  display: flex;
  position: absolute;
  top: 12px;
  right: 12px;
  gap: 4px;
  border-radius: 6px;
  background-color: lch(10.633 1.867 272 / 0.8);
  backdrop-filter: saturate(1.8) blur(20px);
  -webkit-backdrop-filter: saturate(1.8) blur(20px);
  border: 0.5px solid rgba(255, 255, 255, 0.2);
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .tool-icon-container {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    border-radius: 4px;
    padding: 4px;
    font-size: 16px;

    svg {
      stroke: lch(64.892% 1.933 272 / 1);
    }

    &:hover {
      background-color: lch(19.383 5.367 272);

      svg {
        stroke: lch(91.223 1.933 272);
      }
    }
  }
}

.node-image-container {
  position: relative;
  border-color: transparent;

  &:hover {
    .image-preivew-tool,
    .left-drag-line,
    .right-drag-line {
      opacity: 0.8;
    }
  }

  &.dragging-left .left-drag-line,
  &.dragging-right .right-drag-line {
    opacity: 0.8;
  }
}
