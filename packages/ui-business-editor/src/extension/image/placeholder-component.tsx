import { DIFF_CHANGE_ATTR_NAME, Paragraph } from '@repo/editor-common';
import { cn } from '@repo/ui/lib/utils';
import type { NodeViewProps } from '@tiptap/react';

interface PlaceholderComponentProps {
  className?: string;
  nodeViewProps: NodeViewProps;
  position: 'top' | 'bottom';
}

export const PlaceholderComponent = ({
  className,
  nodeViewProps,
  position,
}: PlaceholderComponentProps) => {
  const { node, editor, getPos } = nodeViewProps;

  const insertParagraph = () => {
    if (!editor.isEditable || node.attrs[DIFF_CHANGE_ATTR_NAME]) return;

    const pos = getPos();
    if (typeof pos !== 'number') return;

    const $pos = editor.state.doc.resolve(pos);
    const index = $pos.index(0);
    const targetPos = position === 'top' ? pos : pos + node.nodeSize;

    // 检查目标位置是否已存在空段落
    let targetNode;
    if (position === 'top' && index > 0) {
      targetNode = $pos.node(0).child(index - 1);
    } else if (position === 'bottom' && index < $pos.node(0).childCount - 1) {
      targetNode = $pos.node(0).child(index + 1);
    }

    // 如果已存在空段落，直接聚焦；否则插入新段落
    if (targetNode?.type.name === Paragraph.name && targetNode.content.size === 0) {
      editor.commands.focus(targetPos);
    } else {
      editor
        .chain()
        .insertContentAt(targetPos, { type: Paragraph.name })
        .focus(targetPos + 1)
        .run();
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();
    insertParagraph();
  };

  return (
    <div
      contentEditable={false}
      className={cn('bg-transparent cursor-text h-[10px]', className)}
      onClick={handleClick}
    />
  );
};
