import { type ImageAttrs, ImageStatus } from '@repo/editor-common';
import type { NodeViewProps } from '@tiptap/react';
import { useEffect, useRef, useState } from 'react';
import { type ImageUploadController, UploadImageStatus } from './image-upload-controller';
import {
  calculateImageDisplaySize,
  getFileFromBase64,
  getFileFromImageUrl,
  type TransferFileRes,
} from './utils';

interface UploadingImageNodeProps extends NodeViewProps {
  isExternalImage: boolean;
  isBase64Image: boolean;
}

export const UploadingImageNode = (props: UploadingImageNodeProps) => {
  const completeUpload = useRef(false);
  const { editor, isExternalImage, isBase64Image } = props;
  const uploadingImage = editor.storage.image.uploadingImageMap.get(props.node.attrs.id);
  const { width, height, src } = props.node.attrs as ImageAttrs;
  const imageRef = useRef<HTMLImageElement>(null);
  const [displayWidth, setDisplayWidth] = useState(width);
  const [displayHeight, setDisplayHeight] = useState(height);

  const getUploadFile = async (): Promise<TransferFileRes> => {
    if (uploadingImage?.file) {
      return {
        file: uploadingImage?.file,
        success: true,
      };
    }
    if (!src) {
      return {
        file: null,
        success: false,
      };
    }
    if (isExternalImage) {
      return await getFileFromImageUrl(src);
    }
    if (isBase64Image) {
      return getFileFromBase64(src);
    }
    return {
      file: null,
      success: false,
    };
  };

  useEffect(() => {
    const uploadImage = async () => {
      const imageUploadController = editor.storage.image
        .imageUploadController as ImageUploadController;
      if (!imageUploadController) return;
      const { file: uploadFile } = await getUploadFile();
      if (!uploadFile) {
        props.updateAttributes({
          status: ImageStatus.FAILED,
        });
        return;
      }
      const { status, url } = await imageUploadController.uploadImage(uploadFile);
      if (status === UploadImageStatus.UPLOADED) {
        props.updateAttributes({
          src: url,
          status: ImageStatus.LOADING,
          alt: uploadingImage?.file.name,
        });

        if ((isBase64Image || isExternalImage) && uploadFile) {
          const imageId = props.node.attrs.id;

          const reader = new FileReader();
          const dataUrlPromise = new Promise<string>((resolve) => {
            reader.onload = () => {
              if (typeof reader.result === 'string') {
                resolve(reader.result);
              }
            };
            reader.readAsDataURL(uploadFile);
          });

          dataUrlPromise.then((dataUrl) => {
            editor.storage.image.uploadingImageMap.set(imageId, {
              dataUrl,
              file: uploadFile,
            });
          });
          await dataUrlPromise;
        }

        imageUploadController.extractImage(url).then((text) => {
          props.updateAttributes({
            text: text,
          });
        });
        completeUpload.current = true;
      }
    };
    uploadImage();
  }, []);

  const showImage = uploadingImage || isExternalImage || isBase64Image;

  const getImageUrl = () => {
    if (uploadingImage) {
      return uploadingImage?.dataUrl;
    }
    if (isExternalImage) {
      return src;
    }
    if (isBase64Image) {
      return src;
    }
  };

  const onImageLoad = () => {
    if (props.node.attrs.width && props.node.attrs.height) {
      return;
    }
    if (imageRef.current) {
      const { naturalWidth, naturalHeight } = imageRef.current;

      if (naturalWidth && naturalHeight && naturalWidth > 0 && naturalHeight > 0) {
        const { imageDisplayWidth, imageDisplayHeight } = calculateImageDisplaySize(
          naturalWidth,
          naturalHeight,
        );

        setDisplayWidth(imageDisplayWidth);
        setDisplayHeight(imageDisplayHeight);
        props.updateAttributes({
          width: imageDisplayWidth,
          height: imageDisplayHeight,
        });
      } else {
        props.updateAttributes({
          status: ImageStatus.FAILED,
        });
      }
    }
  };

  return (
    <div className="image-uploading-view relative flex min-h-[120px] max-w-full flex-col overflow-hidden rounded-lg border-2 border-transparent">
      {showImage && (
        <img
          style={{
            width: displayWidth,
            aspectRatio: displayWidth && displayHeight ? displayWidth / displayHeight : undefined,
          }}
          ref={imageRef}
          onLoad={onImageLoad}
          className="w-full max-w-full h-full opacity-50"
          src={getImageUrl()}
          alt={uploadingImage?.file?.name}
        />
      )}
      <div className="flex absolute flex-col gap-3 justify-center items-center w-full h-full">
        <div className="dot-pulse" />
      </div>
    </div>
  );
};
