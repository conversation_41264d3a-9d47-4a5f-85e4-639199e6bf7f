import { Extension } from '@tiptap/core';

export const HeadingHandlerExtensionName = 'headingHandler';

export const HeadingHandler = Extension.create({
  name: HeadingHandlerExtensionName,

  addKeyboardShortcuts() {
    return {
      // 当用户输入空格时触发
      ' ': ({ editor }) => {
        // 检查是否在 heading 节点内
        if (!editor.isActive('heading')) {
          return false;
        }

        const { selection } = editor.state;
        const { $from } = selection;

        const textBefore = editor.state.doc.textBetween($from.start(), $from.pos, null, ' ');

        if (!['-', '*', '+'].includes(textBefore)) {
          return false;
        }

        // 存储当前选区位置
        const pos = selection.$from.pos;

        // 将删除和转换操作合并为一个事务
        editor
          .chain()
          .deleteRange({
            from: pos - 2, // 删除 "-" 和空格
            to: pos,
          })
          .toggleBulletList()
          .run();

        return true;
      },
    };
  },
});
