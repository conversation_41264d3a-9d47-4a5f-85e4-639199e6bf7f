import type { LinkOptions } from '@repo/editor-common';
import { type Editor, ReactRenderer } from '@tiptap/react';
import { createRoot } from 'react-dom/client';
import tippy, { type Instance } from 'tippy.js';
import { findViewContainer } from '../utils';
import { LinkEditPanel } from './link-edit-panel';
import { LinkPanel } from './link-panel';
import { LinkUrlEditPanel } from './link-url-edit-panel';

class HoverPanelManager {
  private tippyInstance: Instance | null = null;
  private hideTimer: NodeJS.Timeout | null = null;
  private scrollHandler = () => this.tippyInstance?.hide();
  private scrollContainer: HTMLElement | null = null;

  constructor(
    private editor: Editor,
    private options: LinkOptions['hoverMenuOptions'],
    private onLinkChange: (link: HTMLAnchorElement | null) => void,
    private onOpenEditPanel: (containerRect: DOMRect, link: HTMLAnchorElement) => void,
  ) {}

  private clearHideTimer() {
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
  }

  private startHideTimer() {
    this.clearHideTimer();
    this.hideTimer = setTimeout(() => {
      this.tippyInstance?.hide();
      this.hideTimer = null;
    }, 500);
  }

  show(link: HTMLAnchorElement, mousePosition: { x: number; y: number }) {
    this.tippyInstance?.hide();
    this.onLinkChange(link);
    this.clearHideTimer();

    const { x, y } = mousePosition;
    const scrollContainer = findViewContainer(this.editor.view.dom);
    this.scrollContainer = scrollContainer;

    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - y;
    const placement = spaceBelow < 80 ? 'top' : 'bottom';
    const linkRect = link.getBoundingClientRect();

    const component = new ReactRenderer(LinkPanel, {
      editor: this.editor,
      props: {
        link,
        editor: this.editor,
        hide: () => this.tippyInstance?.hide(),
        openLinkEditPanel: this.onOpenEditPanel,
        options: this.options,
      },
    });

    this.tippyInstance = tippy(document.body, {
      content: component.element,
      hideOnClick: false,
      allowHTML: true,
      placement,
      trigger: 'manual',
      showOnCreate: true,
      appendTo: () => this.editor.view.dom.parentElement!,
      onHidden: () => {
        this.onLinkChange(null);
        this.scrollContainer?.removeEventListener('scroll', this.scrollHandler);
        link.removeEventListener('mouseleave', this.startHideTimer);
        link.removeEventListener('mouseenter', this.clearHideTimer);
        component.element.removeEventListener('mouseenter', this.clearHideTimer);
        component.element.removeEventListener('mouseleave', this.startHideTimer);
      },
      getReferenceClientRect: () => {
        return new DOMRect(x, placement === 'top' ? linkRect.top + 8 : linkRect.bottom - 4, 0, 0);
      },
      onShow: (instance) => {
        const box = instance.popper.firstElementChild as HTMLElement;
        if (component.element) {
          box.style.pointerEvents = 'auto';
          component.element.addEventListener('mouseenter', () => this.clearHideTimer());
          component.element.addEventListener('mouseleave', () => this.startHideTimer());
        }
        this.scrollContainer?.addEventListener('scroll', this.scrollHandler);
      },
    });

    component.element.classList.add('shadow-lg', 'rounded-lg');
    link.addEventListener('mouseleave', () => this.startHideTimer());
    link.addEventListener('mouseenter', () => this.clearHideTimer());
  }

  hidePanel() {
    this.clearHideTimer();
    this.tippyInstance?.hide();
  }

  destroy() {
    this.clearHideTimer();
    this.tippyInstance?.destroy();
    this.tippyInstance = null;
    this.scrollContainer = null;
  }
}

export class LinkPanelController {
  private linkEl: HTMLAnchorElement | null = null;
  private hoverPanel: HoverPanelManager;

  constructor(
    private editor: Editor,
    private options: LinkOptions,
  ) {
    this.hoverPanel = new HoverPanelManager(
      editor,
      options.hoverMenuOptions,
      (link) => {
        this.linkEl = link;
      },
      (containerRect, link) => this.showLinkEditPanel(containerRect, link),
    );
  }

  private shouldShowLinkHoverPanel(link: HTMLAnchorElement) {
    return link !== this.linkEl;
  }

  showLinkEditPanel(containerRect: DOMRect, link: HTMLAnchorElement) {
    const containerEl = document.createElement('div');
    document.body.appendChild(containerEl);
    const root = createRoot(containerEl);
    root.render(
      <LinkEditPanel
        closePanel={() => {
          root.unmount();
          containerEl.remove();
        }}
        editor={this.editor}
        link={link}
        containerRect={containerRect}
      />,
    );
  }

  showLinkHoverPanel(link: HTMLAnchorElement, mousePosition: { x: number; y: number }) {
    if (!this.shouldShowLinkHoverPanel(link)) return;
    this.hoverPanel.show(link, mousePosition);
  }

  showLinkUrlEditPanel() {
    const containerEl = document.createElement('div');
    containerEl.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    background-color: transparent;
  `;
    document.body.appendChild(containerEl);
    const root = createRoot(containerEl);

    // 根据选区位置计算面板位置
    const { view } = this.editor;
    const { state } = view;
    const { from, to } = state.selection;

    // 获取选区的 DOM 位置
    const startCoords = view.coordsAtPos(from);
    const endCoords = view.coordsAtPos(to);

    const panelWidth = 328;
    const panelHeight = 40;
    const gap = 12;

    // 初始位置：y 轴在选区下方，x 轴取选区起始位置
    let top = Math.max(startCoords.bottom, endCoords.bottom) + gap;
    let left = startCoords.left;

    // 检查下方是否有足够空间
    if (top + panelHeight > window.innerHeight) {
      // 尝试放在选区上方
      const topY = Math.min(startCoords.top, endCoords.top) - panelHeight - gap;
      if (topY >= 16) {
        top = topY;
      } else {
        // 如果上方也不够，则放在可视区域内
        top = window.innerHeight - panelHeight - 16;
      }
    }

    // 检查右侧是否有足够空间
    if (left + panelWidth > window.innerWidth) {
      left = window.innerWidth - panelWidth - 16;
    }

    // 检查左侧是否超出
    if (left < 16) {
      left = 16;
    }

    // 检查上方是否超出
    if (top < 16) {
      top = 16;
    }

    const position = { top, left };

    root.render(
      <LinkUrlEditPanel
        editor={this.editor}
        initialPosition={position}
        closePanel={() => {
          root.unmount();
          containerEl.remove();
        }}
      />,
    );
  }

  hideHoverPanel() {
    this.hoverPanel.hidePanel();
  }

  destroy() {
    this.hoverPanel.destroy();
    this.linkEl = null;
  }
}
