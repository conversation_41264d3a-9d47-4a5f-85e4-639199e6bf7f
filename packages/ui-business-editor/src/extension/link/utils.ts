import type { Editor } from '@tiptap/core';

export const MAX_URL_LENGTH = 1000;

export const formatUrl = (url: string): string => {
  const trimmedUrl = url.trim();
  if (!trimmedUrl) return '';

  try {
    if (
      trimmedUrl.startsWith('http://') ||
      trimmedUrl.startsWith('https://') ||
      trimmedUrl.startsWith('mailto:')
    ) {
      return trimmedUrl;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(trimmedUrl)) {
      return `mailto:${trimmedUrl}`;
    }

    const domainRegex =
      /^(www\.)?([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z0-9][-a-zA-Z0-9]*(\.[a-zA-Z]{2,})+([/?#].*)?$/;
    if (domainRegex.test(trimmedUrl)) {
      return `https://${trimmedUrl}`;
    }

    if (
      trimmedUrl.startsWith('localhost') ||
      /^(\d{1,3}\.){3}\d{1,3}(:\d+)?(\/.*)?$/.test(trimmedUrl) ||
      trimmedUrl.startsWith('file://')
    ) {
      return `http://${trimmedUrl}`;
    }

    const withWww = trimmedUrl.startsWith('www.') ? trimmedUrl : `www.${trimmedUrl}`;
    return `https://${withWww}`;
  } catch (error) {
    return trimmedUrl;
  }
};

interface UpdateEditorLinkOptions {
  editor: Editor;
  url?: string;
  title?: string;
  link?: HTMLAnchorElement;
}

export const updateEditorLink = ({ editor, url, title, link }: UpdateEditorLinkOptions) => {
  try {
    const finalUrl = url || (link ? link.getAttribute('href') : '') || '';
    const finalTitle = title || (link ? link.textContent : '') || '';

    if (link) {
      const view = editor.view;
      const pos = view.posAtDOM(link, 0);

      if (pos !== null && pos >= 0) {
        const node = view.state.doc.nodeAt(pos);
        if (node) {
          return editor
            .chain()
            .setTextSelection({ from: pos, to: pos + node.nodeSize })
            .unsetLink()
            .deleteSelection()
            .insertContent([
              {
                type: 'text',
                marks: [{ type: 'link', attrs: { href: finalUrl } }],
                text: finalTitle,
              },
            ])
            .run();
        }
      }
    }

    // If no link is provided or link position cannot be found, insert new link at current selection
    return editor
      .chain()
      .focus()
      .insertContent([
        {
          type: 'text',
          marks: [{ type: 'link', attrs: { href: finalUrl } }],
          text: finalTitle,
        },
      ])
      .run();
  } catch (error) {
    console.error('Error updating link:', error);
    return false;
  }
};
