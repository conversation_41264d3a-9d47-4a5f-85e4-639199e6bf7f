import './link-style.css';
import type { LinkOptions } from '@repo/editor-common';
import { Link as LinkBase } from '@repo/editor-common';
import type { Mark, MarkType } from '@tiptap/pm/model';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import type { EditorView } from '@tiptap/pm/view';
import { debounce } from 'lodash-es';
import {
  createLinkPanelPlugin,
  type LinkPanelState,
  linkPanelPluginKey,
} from './link-panel-plugin';

export { linkPanelPluginKey };

const showLinkHoverPanel = debounce(
  (link: HTMLAnchorElement, position: { x: number; y: number }, state: LinkPanelState) => {
    state.linkPanelController.showLinkHoverPanel(link, position);
  },
  1000,
);

export const Link = LinkBase.extend<LinkOptions>({
  addOptions() {
    return {
      ...this.parent?.(),
      HTMLAttributes: {
        class: 'youmind-editor-mark-link-ui',
      },
    };
  },

  addProseMirrorPlugins() {
    const plugins = this.parent?.() || [];
    const editor = this.editor;

    return [
      ...plugins,
      createLinkPanelPlugin(editor, this.options),
      new Plugin({
        key: new PluginKey('link-hover-menu'),
        props: {
          handleDOMEvents: {
            mouseover: (_view: EditorView, event) => {
              if (!editor.isEditable) return false;
              const target = event.target as HTMLElement;
              const link = target.closest('a');

              if (!link) return false;

              if (link.classList.contains('ProseMirror-widget')) return false;

              const mouseX = event.clientX;
              const mouseY = event.clientY;

              const state = linkPanelPluginKey.getState(editor.state);
              if (state) {
                showLinkHoverPanel(
                  link,
                  {
                    x: mouseX,
                    y: mouseY,
                  },
                  state,
                );
              }

              return false;
            },
            mouseout: (_view: EditorView, event) => {
              const target = event.target as HTMLElement;
              const link = target.closest('a');

              if (!link) return false;

              // Cancel the debounced show panel call
              showLinkHoverPanel.cancel();

              return false;
            },
          },
        },
      }),
      new Plugin({
        key: new PluginKey('link-space-handle'),
        props: {
          handleKeyDown: (view: EditorView, event: KeyboardEvent) => {
            if (event.key.length !== 1) return false;

            const { state } = view;
            const { selection } = state;
            const { $from } = selection;

            const linkMark = state.schema.marks.link;
            const linkMarkAtPos = $from.marks().find((mark: Mark) => mark.type === linkMark);

            if (linkMarkAtPos) {
              const nextPos = $from.pos + 1;
              if (nextPos <= state.doc.content.size) {
                const resolvedNextPos = state.doc.resolve(nextPos);
                const nextLinkMark = resolvedNextPos
                  .marks()
                  .find(
                    (mark: Mark) =>
                      mark.type === linkMark && mark.attrs.href === linkMarkAtPos.attrs.href,
                  );

                if (!nextLinkMark) {
                  const tr = state.tr.removeStoredMark(linkMark as MarkType);
                  view.dispatch(tr);
                }
              }
            }

            return false;
          },
        },
      }),
    ];
  },
});
