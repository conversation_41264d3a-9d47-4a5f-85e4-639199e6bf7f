/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface HtmlSelectionDto
 */
export interface HtmlSelectionDto {
  /**
   * 匹配的文本
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  match_text?: string;
  /**
   * 匹配的索引
   * @type {number}
   * @memberof HtmlSelectionDto
   */
  match_index?: number;
  /**
   * hashID
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  hash_id?: string;
  /**
   * 颜色
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  color?: string;
}

/**
 * Check if a given object implements the HtmlSelectionDto interface.
 */
export function instanceOfHtmlSelectionDto(value: object): value is HtmlSelectionDto {
  return true;
}

export function HtmlSelectionDtoFromJSON(json: any): HtmlSelectionDto {
  return HtmlSelectionDtoFromJSONTyped(json, false);
}

export function HtmlSelectionDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): HtmlSelectionDto {
  if (json == null) {
    return json;
  }
  return {
    match_text: json.matchText == null ? undefined : json.matchText,
    match_index: json.matchIndex == null ? undefined : json.matchIndex,
    hash_id: json.hashID == null ? undefined : json.hashID,
    color: json.color == null ? undefined : json.color,
  };
}

export function HtmlSelectionDtoToJSON(json: any): HtmlSelectionDto {
  return HtmlSelectionDtoToJSONTyped(json, false);
}

export function HtmlSelectionDtoToJSONTyped(
  value?: HtmlSelectionDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    matchText: value.match_text,
    matchIndex: value.match_index,
    hashID: value.hash_id,
    color: value.color,
  };
}
