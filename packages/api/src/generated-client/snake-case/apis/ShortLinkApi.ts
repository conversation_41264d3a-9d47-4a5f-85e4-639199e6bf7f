/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type { GetShortLinkDto } from '../models/index';
import { GetShortLinkDtoFromJSON, GetShortLinkDtoToJSON } from '../models/index';

export interface ShortLinkControllerGetShortLinkRequest {
  getShortLinkDto: GetShortLinkDto;
}

/**
 * ShortLinkApi - interface
 *
 * @export
 * @interface ShortLinkApiInterface
 */
export interface ShortLinkApiInterface {
  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * @summary 获取实体的短链接
   * @param {GetShortLinkDto} getShortLinkDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ShortLinkApiInterface
   */
  getShortLinkRaw(
    requestParameters: ShortLinkControllerGetShortLinkRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<string>>;

  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  getShortLink(
    getShortLinkDto: GetShortLinkDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<string>;
}

/**
 *
 */
export class ShortLinkApi extends runtime.BaseAPI implements ShortLinkApiInterface {
  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  async getShortLinkRaw(
    requestParameters: ShortLinkControllerGetShortLinkRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<string>> {
    if (requestParameters.getShortLinkDto == null) {
      throw new runtime.RequiredError(
        'getShortLinkDto',
        'Required parameter "getShortLinkDto" was null or undefined when calling getShortLink().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/getShortLink`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
        body: GetShortLinkDtoToJSON(requestParameters.getShortLinkDto),
      },
      initOverrides,
    );

    if (this.isJsonMime(response.headers.get('content-type'))) {
      return new runtime.JSONApiResponse<string>(response);
    } else {
      return new runtime.TextApiResponse(response) as any;
    }
  }

  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  async getShortLink(
    getShortLinkDto: GetShortLinkDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<string> {
    const response = await this.getShortLinkRaw(
      { getShortLinkDto: getShortLinkDto },
      initOverrides,
    );
    return await response.value();
  }
}
