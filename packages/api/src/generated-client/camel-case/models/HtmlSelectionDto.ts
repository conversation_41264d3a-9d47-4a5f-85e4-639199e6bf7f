/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface HtmlSelectionDto
 */
export interface HtmlSelectionDto {
  /**
   * 匹配的文本
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  matchText?: string;
  /**
   * 匹配的索引
   * @type {number}
   * @memberof HtmlSelectionDto
   */
  matchIndex?: number;
  /**
   * hashID
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  hashID?: string;
  /**
   * 颜色
   * @type {string}
   * @memberof HtmlSelectionDto
   */
  color?: string;
}

/**
 * Check if a given object implements the HtmlSelectionDto interface.
 */
export function instanceOfHtmlSelectionDto(value: object): value is HtmlSelectionDto {
  return true;
}

export function HtmlSelectionDtoFromJSON(json: any): HtmlSelectionDto {
  return HtmlSelectionDtoFromJSONTyped(json, false);
}

export function HtmlSelectionDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): HtmlSelectionDto {
  if (json == null) {
    return json;
  }
  return {
    matchText: json.matchText == null ? undefined : json.matchText,
    matchIndex: json.matchIndex == null ? undefined : json.matchIndex,
    hashID: json.hashID == null ? undefined : json.hashID,
    color: json.color == null ? undefined : json.color,
  };
}

export function HtmlSelectionDtoToJSON(json: any): HtmlSelectionDto {
  return HtmlSelectionDtoToJSONTyped(json, false);
}

export function HtmlSelectionDtoToJSONTyped(
  value?: HtmlSelectionDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    matchText: value.matchText,
    matchIndex: value.matchIndex,
    hashID: value.hashID,
    color: value.color,
  };
}
