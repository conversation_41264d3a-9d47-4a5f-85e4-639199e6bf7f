/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type { GetShortLinkDto } from '../models/index';
import { GetShortLinkDtoFromJSON, GetShortLinkDtoToJSON } from '../models/index';

export interface ShortLinkControllerGetShortLinkRequest {
  getShortLinkDto: GetShortLinkDto;
  xUseCamelCase: ShortLinkControllerGetShortLinkXUseCamelCaseEnum;
}

/**
 * ShortLinkApi - interface
 *
 * @export
 * @interface ShortLinkApiInterface
 */
export interface ShortLinkApiInterface {
  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * @summary 获取实体的短链接
   * @param {GetShortLinkDto} getShortLinkDto
   * @param {'true' | 'false'} xUseCamelCase Use camelCase format for response data (default: true)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ShortLinkApiInterface
   */
  getShortLinkRaw(
    requestParameters: ShortLinkControllerGetShortLinkRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<string>>;

  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  getShortLink(
    getShortLinkDto: GetShortLinkDto,
    xUseCamelCase: ShortLinkControllerGetShortLinkXUseCamelCaseEnum,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<string>;
}

/**
 *
 */
export class ShortLinkApi extends runtime.BaseAPI implements ShortLinkApiInterface {
  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  async getShortLinkRaw(
    requestParameters: ShortLinkControllerGetShortLinkRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<string>> {
    if (requestParameters.getShortLinkDto == null) {
      throw new runtime.RequiredError(
        'getShortLinkDto',
        'Required parameter "getShortLinkDto" was null or undefined when calling getShortLink().',
      );
    }

    if (requestParameters.xUseCamelCase == null) {
      throw new runtime.RequiredError(
        'xUseCamelCase',
        'Required parameter "xUseCamelCase" was null or undefined when calling getShortLink().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    if (requestParameters.xUseCamelCase != null) {
      headerParameters['x-use-camel-case'] = String(requestParameters.xUseCamelCase);
    }

    const urlPath = `/api/v1/getShortLink`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
        body: GetShortLinkDtoToJSON(requestParameters.getShortLinkDto),
      },
      initOverrides,
    );

    if (this.isJsonMime(response.headers.get('content-type'))) {
      return new runtime.JSONApiResponse<string>(response);
    } else {
      return new runtime.TextApiResponse(response) as any;
    }
  }

  /**
   * 根据实体类型和ID获取短链接，若不存在则返回null
   * 获取实体的短链接
   */
  async getShortLink(
    getShortLinkDto: GetShortLinkDto,
    xUseCamelCase: ShortLinkControllerGetShortLinkXUseCamelCaseEnum,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<string> {
    const response = await this.getShortLinkRaw(
      { getShortLinkDto: getShortLinkDto, xUseCamelCase: xUseCamelCase },
      initOverrides,
    );
    return await response.value();
  }
}

/**
 * @export
 * @enum {string}
 */
export enum ShortLinkControllerGetShortLinkXUseCamelCaseEnum {
  true = 'true',
  false = 'false',
  unknownDefaultOpenApi = '11184809',
}
