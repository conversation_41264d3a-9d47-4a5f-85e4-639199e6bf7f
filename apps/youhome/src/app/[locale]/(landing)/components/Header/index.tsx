'use client';

import { usePathname } from '@i18n/navigation';
import { TopBanner } from '@repo/ui/components/custom/top-banner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { getBrowserName } from '@repo/ui/lib/browser';
import { LogOut } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { logOut } from '@/lib/client/utils';
import { apiClient } from '@/lib/request/client';
import { UserVO } from '@/schema/userSchema';
import { GameStartButton } from '../../overview/components/GameStartButton';
import { YouMind } from '../YouMind';

interface HeaderProps {
  user?: UserVO;
}

export const Header = ({ user: initialUser }: HeaderProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState<UserVO | undefined>(initialUser);
  const pathname = usePathname();

  useEffect(() => {
    if (!initialUser) {
      const fetchUser = async () => {
        try {
          const data = await apiClient.userApi.getCurrentUser();
          // 映射 UserWithPreferenceSpaceDto 到 UserVO 格式
          const userVO: UserVO = {
            id: data.id,
            email: data.email,
            name: data.name,
            picture: data.picture,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
            timeZone: data.timeZone,
            confirmedAt: data.confirmedAt,
            lastSignInAt: data.lastSignInAt,
          };
          setUser(userVO);
        } catch (error) {
          // 静默处理错误，不显示错误信息
          console.error('Failed to fetch user:', error);
        }
      };
      fetchUser();
    }
  }, [initialUser]);

  useEffect(() => {
    if (sidebarOpen) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }

    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [sidebarOpen]);

  const headerNavs = [
    {
      title: 'Overview',
      href: '/overview',
    },
    {
      title: 'Use cases',
      href: '/use-cases',
    },
    {
      title: 'Pricing',
      href: '/pricing',
    },
    {
      title: 'Blog',
      href: '/blog',
    },
    {
      title: 'Updates',
      href: '/updates',
    },
  ];

  const browserName = getBrowserName();

  let addExtensionText = `Add to ${browserName}`;
  if (browserName === 'Safari') {
    addExtensionText = 'Get Chrome extension';
  }

  return (
    <div data-focus-lock-disabled="disabled">
      <div className="pointer-events-auto fixed left-0 right-0 top-0 z-[101] w-full bg-white font-sans-title backdrop-blur-md">
        <TopBanner uid="xiaohongshu-2025-07" />
        <div className="flex h-14 w-full flex-row items-center justify-between px-3 font-medium md:!px-8">
          <Link
            className="flex w-0 flex-shrink-0 flex-grow flex-row items-center space-x-3 rounded-full outline-offset-8 outline-white"
            href="/"
          >
            <YouMind />
          </Link>

          <div
            style={{ flexGrow: 2 }}
            className="flex h-full w-0 flex-row items-center justify-center gap-4"
          >
            {headerNavs.map((nav, index) => {
              const isActive = pathname === nav.href || pathname?.startsWith(`${nav.href}/`);
              return (
                <Link
                  key={index}
                  className="hidden rounded-full text-sm outline-offset-8 outline-white md:!block"
                  href={nav.href}
                >
                  <button
                    className={`h-[28px] rounded-full px-4 text-sm transition-all ${
                      isActive ? 'text-foreground' : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {nav.title}
                  </button>
                </Link>
              );
            })}
          </div>

          <div className="flex w-0 flex-shrink-0 flex-grow flex-row items-center justify-end space-x-3">
            {user ? (
              <div className="bg-primary-900 outline-primary-900 hidden rounded-full px-3 py-1.5 text-sm outline-offset-4 md:block">
                <DropdownMenu>
                  <DropdownMenuTrigger>{user.name || user.email}</DropdownMenuTrigger>
                  <DropdownMenuContent className="z-[102]">
                    <DropdownMenuItem className="cursor-pointer space-x-1" onClick={logOut}>
                      <LogOut className="h-4 w-4" />
                      <span>Sign out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <Link
                className="bg-primary-900 outline-primary-900 hidden rounded-full px-3 py-1.5 text-sm outline-offset-4 md:block"
                href="/sign-in"
              >
                Sign in
              </Link>
            )}
            {process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL && (
              <Link
                className="hidden md:!block"
                href={process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL}
                target="_blank"
              >
                <button className="h-[36px] flex-shrink-0 rounded-full bg-[#000000E0] px-4 text-sm font-medium text-white transition-all duration-300 hover:scale-105">
                  {addExtensionText}
                </button>
              </Link>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="h-full pl-6 pr-2 md:hidden"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="transition-transform duration-300 ease-in-out"
                style={{
                  transform: sidebarOpen ? 'rotate(180deg)' : 'rotate(0)',
                }}
              >
                <path
                  d="M3 6h18M3 12h18M3 18h18"
                  stroke="black"
                  strokeOpacity="0.88"
                  strokeWidth="2"
                  strokeLinecap="round"
                  className={`transition-all duration-300 ease-in-out ${
                    sidebarOpen ? 'opacity-0' : 'opacity-100'
                  }`}
                />
                <path
                  d="M6 6l12 12M6 18L18 6"
                  stroke="black"
                  strokeOpacity="0.88"
                  strokeWidth="2"
                  strokeLinecap="round"
                  className={`transition-all duration-300 ease-in-out ${
                    sidebarOpen ? 'opacity-100' : 'opacity-0'
                  }`}
                />
              </svg>
            </button>
            {!user && (
              <div className="block md:!hidden">
                <GameStartButton
                  text="Start"
                  className="h-[28px] rounded-full bg-[#000000E0] px-4 text-sm text-white"
                />
              </div>
            )}
          </div>
        </div>
        <div
          className="pointer-events-none absolute bottom-[0px] left-0 right-0 z-[32] h-[1px] w-full bg-neutral-300 transition-all duration-300 md:hidden"
          style={{ opacity: sidebarOpen ? 1 : 0, willChange: 'auto' }}
        ></div>
      </div>

      {/* 移动端的菜单 */}
      <div
        className="pointer-events-none fixed right-0 top-14 z-[101] h-screen w-full overflow-hidden transition-all duration-300 md:hidden"
        style={{ width: sidebarOpen ? '100vw' : '0vw', willChange: 'auto' }}
      >
        <div className="pointer-events-auto absolute bottom-0 left-0 top-0 flex w-screen flex-col overflow-y-scroll bg-[#F7F7F8]/80 pl-6 backdrop-blur-md">
          {headerNavs.map((nav, index) => {
            const isActive = pathname === nav.href || pathname?.startsWith(`${nav.href}/`);
            return (
              <Link
                key={index}
                className={`text-l9 border-b border-neutral-300 py-5 pr-6 ${
                  isActive ? 'font-medium text-black' : 'text-gray-700'
                }`}
                href={nav.href}
                onClick={() => setSidebarOpen(false)}
              >
                {nav.title}
              </Link>
            );
          })}
          {/* {process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL && (
            <Link
              className="text-l9 border-b border-neutral-300 py-5 pr-6"
              href={process.env.NEXT_PUBLIC_BROWSER_EXTENSION_URL}
              target="_blank"
              onClick={() => setSidebarOpen(false)}
            >
              Browser Extension
            </Link>
          )} */}
        </div>
      </div>
    </div>
  );
};
