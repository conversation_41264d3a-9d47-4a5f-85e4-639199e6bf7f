import { Link } from '@i18n/navigation';
import { Beach, Book, Education, Ink } from '@youmindinc/youicon';
import Image from 'next/image';
import type { ComponentType } from 'react';
import { getMediaThumbnail, parseRichTextToString } from '@/cms/cms-util';
import { MEDIA_PLACEHOLDER_BLUR } from '@/cms/const';
import { Media, UseCase, UseCasesCategory, Video } from '@/cms/types/payload-raw-types';
import { FileSearch } from '@/components/icon/file-search';

export function getUseCaseIcon(
  useCaseCategorySlug: string,
): ComponentType<{ size: number; className?: string }> | undefined {
  return {
    life: Beach,
    learning: Education,
    pkm: Book,
    writing: Ink,
    research: FileSearch,
  }[useCaseCategorySlug];
}

export function UseCaseList({ useCases, filter }: { useCases: UseCase[]; filter?: string }) {
  const filteredUseCases = useCases.filter((useCase) => {
    if (!filter) return true;
    if (filter === 'featured') {
      return useCase.featured;
    }

    return useCase.categories!.some((category) => (category as UseCasesCategory).slug === filter);
  });
  return (
    <div className="grid grid-cols-1 gap-x-[16px] gap-y-[16px] md:grid-cols-2 md:gap-x-[36px] md:gap-y-[40px] lg:grid-cols-3">
      {filteredUseCases.map((useCase) => (
        <UseCaseItem key={useCase.id} useCase={useCase} />
      ))}
    </div>
  );
}

// function getMediaThumbnail(obj: Video | Media): MediaType | null {
//   if ((obj as Video)?.cloudflareStream?.streamId) {
//     return {
//       type: "video",
//       thumbnailUrl: ((obj as Video).poster as Media).url!,
//     };
//   } else if ((obj as Media)?.url) {
//     return {
//       type: "image",
//       thumbnailUrl: obj.url!,
//     };
//   }

//   return null;
// }

export function UseCaseItem({ useCase }: { useCase: UseCase }) {
  const Icon = getUseCaseIcon((useCase.categories?.[0] as UseCasesCategory)?.slug || 'life')!;

  const coverMedia =
    getMediaThumbnail(useCase.heroVideo as Video) ||
    getMediaThumbnail(useCase.heroImage as Media) ||
    undefined;

  return (
    <Link href={`/use-cases/${useCase.slug}`} className="relative block h-full w-full" prefetch>
      <div className="group flex cursor-pointer flex-col items-center overflow-hidden rounded-[16px] border border-card-snips bg-card-snips px-5 pb-5 pt-8 md:h-[420px] md:px-8">
        <div className="mb-3">
          <Icon size={32} />
        </div>
        <h3 className="mb-2 line-clamp-2 w-full text-center text-2xl font-medium">
          {useCase.title}
        </h3>
        <p className="mb-4 line-clamp-4 max-h-[6rem] text-center text-base text-muted-foreground md:line-clamp-3 md:max-h-[4.5rem]">
          {parseRichTextToString(useCase.content)}
        </p>
        <div className="flex-1"></div>
        {coverMedia && (
          <div className="relative h-[168px] w-full rounded-xl shadow-md">
            {coverMedia?.type === 'video' ? (
              <Image
                src={coverMedia.thumbnailUrl!}
                alt={useCase.title}
                placeholder="blur"
                blurDataURL={MEDIA_PLACEHOLDER_BLUR}
                fill
                className="rounded-xl object-cover transition-transform duration-300 group-hover:scale-105"
              />
            ) : (
              <Image
                src={coverMedia?.thumbnailUrl as string}
                alt={useCase.title}
                placeholder="blur"
                blurDataURL={MEDIA_PLACEHOLDER_BLUR}
                fill
                className="rounded-xl object-cover transition-transform duration-300 group-hover:scale-105"
              />
            )}
          </div>
        )}
      </div>
    </Link>
  );
}
