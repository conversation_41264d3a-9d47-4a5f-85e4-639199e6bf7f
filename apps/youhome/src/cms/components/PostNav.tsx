import { Link } from '@i18n/navigation';
import { ChevronLeft } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PostNavProps {
  backLink?: string;
  backLinkText?: string;
  className?: string;
}

export function PostNav({ backLink = '/blog', backLinkText = 'Blog', className }: PostNavProps) {
  return (
    <div
      className={cn(
        'relative mb-8 flex w-full flex-wrap items-center justify-between text-base text-caption-foreground md:mb-[80px]',
        className,
      )}
    >
      <Link
        href={backLink}
        className="inline-flex cursor-pointer items-center text-base text-foreground"
        prefetch
      >
        <ChevronLeft className="mr-[2px]" size={20} />
        {backLinkText}
      </Link>
    </div>
  );
}
