import type { LangfuseSpanClient } from 'langfuse';
import type { CreateLangfuseTraceBody, LangfuseGenerationClient } from 'langfuse-core';
import type OpenAI from 'openai';
import type {
  ChatCompletionChunk,
  ChatCompletionContentPart,
  ChatCompletionCreateParams,
  ChatCompletionMessageToolCall,
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
  ChatCompletionToolMessageParam,
} from 'openai/resources/index.mjs';
import type { Uploadable } from 'openai/uploads.mjs';
import type { ImageGenerateToolStyle } from '@/common/types';
import { LLMProviders, LLMs } from '@/common/types';
import type { LLMRunner } from './llm_service/runner';
import type { YLBaseLLMProvider } from './provider/base';
import { ProviderOverloadManager } from './provider/keyRotationManager';

// 首先定义所有 MinimaxVoice 的值
const MINIMAX_VOICES_CONST = [
  'Wise_Woman',
  'Friendly_Person',
  'Inspirational_girl',
  'Deep_Voice_Man',
  '<PERSON><PERSON>_Woman',
  '<PERSON><PERSON><PERSON>_Guy',
  'Lively_Girl',
  'Patient_Man',
  '<PERSON>_Knight',
  'Determined_Man',
  'Lovely_Girl',
  'Decent_Boy',
  'Imposing_Manner',
  'Elegant_Man',
  'Abbess',
  'Sweet_Girl_2',
  'Exuberant_Girl',
] as const;

// 从值推导类型
export type MinimaxVoice = (typeof MINIMAX_VOICES_CONST)[number];

// 导出可使用的数组
export const MINIMAX_VOICES = [...MINIMAX_VOICES_CONST];

// 定义所有 MinimaxEmotion 的值
const MINIMAX_EMOTIONS_CONST = [
  'happy',
  'sad',
  'angry',
  'fearful',
  'disgusted',
  'surprised',
  'neutral',
] as const;

// 从值推导类型
export type MinimaxEmotion = (typeof MINIMAX_EMOTIONS_CONST)[number];

// 定义所有 OpenAIVoice 的值
const OPENAI_VOICES_CONST = [
  'alloy',
  'ash',
  'ballad',
  'coral',
  'echo',
  'fable',
  'nova',
  'onyx',
  'sage',
  'shimmer',
] as const;

// 从值推导类型
export type OpenAIVoice = (typeof OPENAI_VOICES_CONST)[number];

export type YLChatRequestMessage =
  | YLChatRequestSystemMessage
  | YLChatRequestUserMessage
  | YLChatRequestAssistantMessage
  | YLChatRequestToolMessage;

export type YLChatCompletionContentPart =
  | ChatCompletionContentPart
  | {
      type: 'thinking';
      thinking: string;
      signature?: string;
    }
  | {
      type: 'redacted_thinking';
      data: string;
    };

export interface YLChatRequestSystemMessage {
  role: 'system';
  content: string | Array<YLChatCompletionContentPart>;
}

export interface YLChatRequestUserMessage {
  role: 'user';
  content: string | Array<YLChatCompletionContentPart>;
}

export interface YLChatRequestAssistantMessage {
  role: 'assistant';
  content: string | Array<YLChatCompletionContentPart>;
  tool_calls?: Array<ChatCompletionMessageToolCall>;
}

export type YLChatRequestToolMessage = ChatCompletionToolMessageParam;

export type YLChatCompletionFinishReason =
  | 'length'
  | 'stop'
  | 'content_filter'
  | 'tool_calls'
  | 'function_call'
  | null;

export interface YLChatCompletionChoice {
  message?: {
    role: string;
    content: string | null;
  };
  finish_reason?: YLChatCompletionFinishReason;
}

export interface YLChatCompletionChunkChoice {
  delta?: {
    role?: string;
    content?: string | null;
    tool_calls?: Array<ChatCompletionChunk.Choice.Delta.ToolCall>;
    thinking?: {
      type: string; // "thinking" | "redacted_thinking";
      signature?: string;
      data?: string | null;
    } | null;
  };
  finish_reason?: 'length' | 'stop' | 'content_filter' | 'tool_calls' | 'function_call' | null;
}

export interface YLChatCompletionOptions {
  model?: string;

  top_p?: number;
  max_tokens?: number;
  temperature?: number;

  tools?: Array<ChatCompletionTool>;
  tool_choice?: ChatCompletionToolChoiceOption;
  response_format?: ChatCompletionCreateParams['response_format'];
  /** 预测输出 */
  prediction?: string[];
  /** fallback 配置 */
  fallbackConfig?: YLFallbackConfig;
}

export type YLCompletionUsage = OpenAI.CompletionUsage;
export type YLEmbeddingsUsage = Pick<OpenAI.CompletionUsage, 'total_tokens'>;

export interface YLChatCompletions {
  id: string;
  choices: YLChatCompletionChoice[];
  usage?: YLCompletionUsage;
  created: Date | number;
  model?: string;
  trace_id?: string;
}

export interface YLChatStreamCompletion {
  id: string;
  choices: YLChatCompletionChunkChoice[];
  usage?: YLCompletionUsage;
  created: Date | number;
  model?: string;
  trace_id?: string;
}

export interface YLEmbeddingsCreation {
  embeddings: Array<Array<number>>;
  usage: YLEmbeddingsUsage;
}

export type YLChatStreamCompletions = AsyncIterable<YLChatStreamCompletion>;

export interface YLTTSOptions {
  voice?: MinimaxVoice | OpenAIVoice;
  response_format?: 'mp3' | 'opus' | 'aac' | 'flac' | 'wav' | 'pcm';
  speed?: number;
  vol?: number;
  pitch?: number;
  model?: string;
  emotion?: MinimaxEmotion;
  sampleRate?: number;
  bitrate?: number;
  channel?: number;
  languageBoost?: string;
  subtitleEnable?: boolean;
  /** langfuse 追踪相关的参数，一般不需要关心 */
  traceArgs?: CreateLangfuseTraceBody;
}

export interface YLDiagramGenerationOptions {
  text: string;
  type: string;
  size: 'auto' | 'square' | 'portrait' | 'landscape';
}

export interface YLDiagramOptimizationOptions {
  type: string;
  svg: string;
}

export interface YLImageGenerationOptions {
  // @see https://platform.openai.com/docs/guides/image-generation#customize-image-output
  size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
  quality: 'low' | 'medium' | 'high' | 'auto';
  // @see https://platform.openai.com/docs/guides/image-generation?image-generation-model=gpt-image-1&lang=javascript#transparency
  background: 'transparent' | 'opaque' | 'auto';
  n: number;
  style: ImageGenerateToolStyle;
}

export interface YLImageEditOptions extends YLImageGenerationOptions {
  mask: Uploadable;
}
export interface PromptConfig extends YLChatCompletionOptions {
  provider?: string;
  [key: string]: unknown;
}

export abstract class AbstractYouConsumer<T> {
  abstract register(handler: ConsumerMessageHandler): void;
  abstract unregister(handler: ConsumerMessageHandler): void;
  abstract isDone(): boolean;
  abstract isFinal(): boolean;
  abstract getUsage(): OpenAI.CompletionUsage;
  abstract getStatus(): ConsumerStatusEnum;
  abstract getCacheable(): unknown;
  abstract getTraceOutput(): unknown;
  abstract getFinishMessage(): T | null;
  abstract consume(generation?: LangfuseGenerationClient): AsyncGenerator<T>;
}

export interface FetchPromptParams {
  name: string;
  label?: string;
  prefer?: 'remote' | 'local';
  variables?: Record<string, string>;
}

export abstract class AbstractYouRunner {
  abstract get model_name(): string;
  abstract get options(): YLChatCompletionOptions | null;
  abstract get messages(): YLChatRequestMessage[];
  abstract get provider(): YLBaseLLMProvider;

  abstract initByPrompt(params: FetchPromptParams, options?: YLCompletionOptions): Promise<void>;
  abstract runSpan<T>(param: {
    name: string;
    input?: object;
    disable_output?: boolean;
    fn: (span: LangfuseSpanClient) => Promise<T>;
  }): Promise<T>;
  abstract generateByConsumer<T>(param: {
    consumer: AbstractYouConsumer<T>;
    name?: string;
    metadata?: Record<string, string>;
  }): AsyncGenerator<T, unknown, unknown>;
  abstract generateOnce(param: {
    name?: string;
    options?: YLCompletionOptions;
    metadata?: Record<string, string>;
  }): AsyncGenerator<OnceMessage, unknown, unknown>;
  abstract generateStream(param: {
    name?: string;
    options?: YLCompletionOptions;
    metadata?: Record<string, string>;
  }): AsyncGenerator<StreamMessage, unknown, unknown>;
  abstract cleanup(): Promise<void>;
}

export type ConsumerMessageHandler = (param: {
  message: unknown;
  consumer: AbstractYouConsumer<unknown>;
  generation?: LangfuseSpanClient;
}) => Promise<void>;

export interface YLFallbackConfig {
  enabled: boolean; // 是否启用降级
  fallbackProvider: LLMProviders; // 降级到的provider
  fallbackModel?: LLMs; // 降级到的model（可选）
  errorCodes?: string[]; // 触发降级的额外错误码
  overloadWindowMs?: number; // 过载状态持续时间（毫秒），默认3分钟
}

export interface YLCompletionOptions {
  /** 是否使用缓存结果，默认为 `true` */
  useCache?: boolean;
  /** 拉取 langfuse 中指定 label 的 prompt，默认为 `production` */
  promptLabel?: string;
  /** 优先使用 prompt 来源 */
  promptPrefer?: 'remote' | 'local';
  /** langfuse 追踪相关的参数，一般不需要关心 */
  traceArgs?: CreateLangfuseTraceBody;
  /** 模型相关的参数，一般不需要关心 */
  modelOptions?: YLChatCompletionOptions;
  /** 历史聊天记录 */
  messages?: YLChatRequestMessage[];
  /** 是否为重新生成，若为 true 则增加 temperature */
  regenerate?: boolean;
  /** 通过外部信号终止输出 */
  abortSignal?: AbortSignal;
  /** 自定义 Runner */
  runner?: LLMRunner;
  /** 消息处理 handler */
  messageHandler?: ConsumerMessageHandler;
  /** usage args */
  usageArgs?: Record<string, string>;
  /** fallback 配置 */
  fallbackConfig?: YLFallbackConfig;
}

export type YLEmbeddingsOptions = Pick<
  YLCompletionOptions,
  'traceArgs' | 'useCache' | 'usageArgs' | 'fallbackConfig'
> & {
  provider?: LLMProviders;
  model?: LLMs;
};

export const DEFAULT_PROVIDER =
  (process.env.DEFAULT_LLM_PROVIDER as LLMProviders) ?? LLMProviders.AZURE_OPENAI;
export const DEFAULT_LLM = (process.env.DEFAULT_LLM_MODEL as LLMs) ?? LLMs.GPT_4O_MINI;
// text-embedding-3-large has a limit of 8192 tokens
export const EMBEDDING_TOKEN_LIMIT = 8192;
export const TTS_CHARACTER_LIMIT = 1024;

export const PROMPT_CACHE_TIME = 300;
export enum SpanEventLevel {
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG',
  DEFAULT = 'DEFAULT',
}
export enum ConsumerStatusEnum {
  ING = 'generating',
  DONE = 'success',
  ERROR = 'errored',
  ABORT = 'aborted',
}

export type StreamMessage =
  | YLChatStreamCompletion
  | (OpenAI.ChatCompletionChunk & { trace_id?: string });
export type OnceMessage = YLChatCompletions | (OpenAI.ChatCompletion & { trace_id?: string });
export type EmbeddingMessage = YLEmbeddingsCreation;

/**
 * 默认的降级配置
 */
export const DEFAULT_FALLBACK_CONFIG: YLFallbackConfig = {
  enabled: true,
  fallbackProvider: LLMProviders.AI_HUB_MIX,
  overloadWindowMs: 3 * 60 * 1000, // 3分钟
  errorCodes: [
    // 标准HTTP错误码
    '503', // Service Unavailable
    '529', // Site Overloaded (Anthropic常用)
    '502', // Bad Gateway (可能表示服务过载)
    '504', // Gateway Timeout

    // 🏛️ Anthropic (Claude) 过载错误
    'overloaded',
    'overloaded_error',
    'system_overload_error',
    'system encountered an overload',

    // 🔍 Google Gemini 过载错误
    'resource has been exhausted',
    'the model is overloaded',
    'goaway received',
    'session_timed_out',

    // 🤖 OpenAI 过载错误
    'server is overloaded',
    'connection error',
    'overflow',

    // 🌐 通用过载模式
    'server overloaded',
    'service overloaded',
    'capacity exceeded',
    'gateway timeout',
    'upstream timeout',
    'service unavailable',

    // 原有错误码保持兼容
    'rate_limit',
    'quota_exceeded',
    'anthropic_api_error',
    'unsupported_countries',
  ],
};

// Export overload manager for debugging and management
export { ProviderOverloadManager };
