/**
 * Content Value Object - 内容值对象
 * 封装文章内容的结构化数据，包括格式、原始内容、纯文本和语言信息
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/types.ts (ReaderHTMLContent)
 * - /youapp/src/lib/app/snip/types.ts (ReaderHTMLContentSchema)
 * - src/modules/material-mng/domain/snip/models/type.ts (content interfaces)
 */

import { ContentFormatEnum, LanguageEnum } from '@repo/common';
import { SafeParse } from '@/common/utils';

export class Content {
  constructor(
    private readonly format: ContentFormatEnum,
    private readonly raw: string,
    private readonly plain?: string,
    private readonly language?: LanguageEnum,
  ) {
    this.validate();
  }

  private validate(): void {
    if (!Object.values(ContentFormatEnum).includes(this.format)) {
      throw new Error(`Invalid content format: ${this.format}`);
    }

    if (typeof this.raw !== 'string') {
      throw new Error('Content raw must be a string');
    }

    if (this.plain !== undefined && typeof this.plain !== 'string') {
      throw new Error('Content plain must be a string or undefined');
    }

    if (this.language !== undefined && !Object.values(LanguageEnum).includes(this.language)) {
      throw new Error(`Invalid language: ${this.language}`);
    }
  }

  public getFormat(): ContentFormatEnum {
    return this.format;
  }

  public getRaw(): string {
    return this.raw;
  }

  public getPlain(): string | undefined {
    return this.plain;
  }

  public getLanguage(): LanguageEnum | undefined {
    return this.language;
  }

  public hasPlainText(): boolean {
    return this.plain !== undefined && this.plain.length > 0;
  }

  public isEmpty(): boolean {
    return this.raw.trim().length === 0;
  }

  public isReaderHTML(): boolean {
    return this.format === ContentFormatEnum.READER_HTML;
  }

  public isRichText(): boolean {
    return this.format === ContentFormatEnum.RICHTEXT;
  }

  public isMessages(): boolean {
    return this.format === ContentFormatEnum.MESSAGES;
  }

  /**
   * 获取用于搜索的文本
   */
  public getSearchableText(): string {
    return this.plain || this.raw;
  }

  /**
   * 获取内容长度
   */
  public getLength(): number {
    return this.getSearchableText().length;
  }

  /**
   * 获取内容摘要
   */
  public getSummary(maxLength: number = 200): string {
    const text = this.getSearchableText();
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  /**
   * 检查内容是否包含指定文本
   */
  public contains(searchText: string, caseSensitive: boolean = false): boolean {
    const text = this.getSearchableText();
    const search = caseSensitive ? searchText : searchText.toLowerCase();
    const content = caseSensitive ? text : text.toLowerCase();
    return content.includes(search);
  }

  public equals(other: Content): boolean {
    return (
      this.format === other.format &&
      this.raw === other.raw &&
      this.plain === other.plain &&
      this.language === other.language
    );
  }

  public toJSON(): {
    format: ContentFormatEnum;
    raw: string;
    plain?: string;
    language?: LanguageEnum;
  } {
    return {
      format: this.format,
      raw: this.raw,
      ...(this.plain !== undefined && { plain: this.plain }),
      ...(this.language !== undefined && { language: this.language }),
    };
  }

  public static fromJSON(data: {
    format: ContentFormatEnum;
    raw: string;
    plain?: string;
    language?: LanguageEnum;
  }): Content {
    return new Content(data.format, data.raw, data.plain, data.language);
  }

  /**
   * 创建 Reader HTML 格式的内容
   */
  public static createReaderHTML(raw: string, plain?: string, language?: LanguageEnum): Content {
    return new Content(ContentFormatEnum.READER_HTML, raw, plain, language);
  }

  /**
   * 创建富文本格式的内容
   */
  public static createRichText(raw: string, plain?: string, language?: LanguageEnum): Content {
    return new Content(ContentFormatEnum.RICHTEXT, raw, plain, language);
  }
}

/**
 * Reader HTML 内容值对象
 * 专门用于处理 Reader HTML 格式的内容
 */
export class ReaderHTMLContent extends Content {
  constructor(raw: string, plain?: string, language?: LanguageEnum) {
    super(ContentFormatEnum.READER_HTML, raw, plain, language);
  }

  /**
   * 从标准内容值对象创建 Reader HTML 内容
   */
  public static fromContent(content: Content): ReaderHTMLContent {
    if (!content.isReaderHTML()) {
      throw new Error('Content must be in READER_HTML format');
    }

    return new ReaderHTMLContent(content.getRaw(), content.getPlain(), content.getLanguage());
  }

  /**
   * 提取图片 URL
   */
  public extractImageUrls(): string[] {
    const imageUrls: string[] = [];
    const plain = this.getPlain();

    if (plain) {
      // 从 markdown 中提取图片
      const markdownImageRegex = /!\[.*?\]\((.*?)\)/g;
      let match;
      while ((match = markdownImageRegex.exec(plain)) !== null) {
        imageUrls.push(match[1]);
      }
    }

    // 从 HTML 中提取图片
    const raw = this.getRaw();
    const htmlImageRegex = /<img[^>]+src="([^"]*)"[^>]*>/g;
    let match;
    while ((match = htmlImageRegex.exec(raw)) !== null) {
      imageUrls.push(match[1]);
    }

    // 去重
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL
   */
  public replaceImageUrls(imageMapping: { originalUrl: string; url: string }[]): ReaderHTMLContent {
    let newRaw = this.getRaw();
    let newPlain = this.getPlain();

    imageMapping.forEach(({ originalUrl, url }) => {
      newRaw = newRaw.replace(new RegExp(originalUrl, 'g'), url);
      if (newPlain) {
        newPlain = newPlain.replace(new RegExp(originalUrl, 'g'), url);
      }
    });

    return new ReaderHTMLContent(newRaw, newPlain, this.getLanguage());
  }
}

/**
 * Richtext Content - 富文本内容
 * 专门用于富文本格式的内容处理
 */
export class RichtextContent extends Content {
  constructor(raw: string, plain?: string, language?: LanguageEnum) {
    super(ContentFormatEnum.RICHTEXT, raw, plain, language);
  }

  /**
   * 从标准内容值对象创建富文本内容
   */
  public static fromContent(content: Content): RichtextContent {
    if (!content.isRichText()) {
      throw new Error('Content must be in RICHTEXT format');
    }

    return new RichtextContent(content.getRaw(), content.getPlain(), content.getLanguage());
  }

  /**
   * 解析富文本中的链接
   */
  public extractLinks(): string[] {
    const links: string[] = [];
    const raw = this.getRaw();

    // 提取 markdown 链接
    const markdownLinkRegex = /\[.*?\]\((https?:\/\/[^\s)]+)\)/g;
    let match;
    while ((match = markdownLinkRegex.exec(raw)) !== null) {
      links.push(match[1]);
    }

    // 提取 HTML 链接
    const htmlLinkRegex = /<a[^>]+href="(https?:\/\/[^"]*)"[^>]*>/g;
    while ((match = htmlLinkRegex.exec(raw)) !== null) {
      links.push(match[1]);
    }

    // 去重
    return Array.from(new Set(links));
  }

  /**
   * 获取富文本的纯文本版本
   */
  public getTextContent(): string {
    const plain = this.getPlain();
    if (plain) {
      return plain;
    }

    // 简单的 HTML 标签移除
    return this.getRaw()
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .trim();
  }
}

/**
 * Messages Content - 消息内容
 * 专门用于消息格式的内容处理
 */
export class MessagesContent extends Content {
  constructor(raw: string, plain?: string, language?: LanguageEnum) {
    super(ContentFormatEnum.MESSAGES, raw, plain, language);
  }

  /**
   * 从标准内容值对象创建消息内容
   */
  public static fromContent(content: Content): MessagesContent {
    if (!content.isMessages()) {
      throw new Error('Content must be in MESSAGES format');
    }

    return new MessagesContent(content.getRaw(), content.getPlain(), content.getLanguage());
  }

  /**
   * 解析消息内容
   * 假设消息内容是 JSON 格式
   */
  public parseMessages(): any[] {
    const parsed = SafeParse(this.getRaw(), false, []);
    return Array.isArray(parsed) ? parsed : [parsed];
  }

  /**
   * 获取消息数量
   */
  public getMessageCount(): number {
    try {
      return this.parseMessages().length;
    } catch {
      return 0;
    }
  }

  /**
   * 获取消息的纯文本内容
   */
  public getMessagesText(): string {
    const plain = this.getPlain();
    if (plain) {
      return plain;
    }

    try {
      const messages = this.parseMessages();
      return messages
        .map((msg: any) => {
          if (typeof msg === 'string') return msg;
          if (msg.content) return msg.content;
          if (msg.text) return msg.text;
          return JSON.stringify(msg);
        })
        .join('\n');
    } catch {
      return this.getRaw();
    }
  }
}
