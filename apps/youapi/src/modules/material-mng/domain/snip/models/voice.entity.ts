/**
 * Voice Entity - Voice 领域实体
 * 音频实体，继承自 Snip 基础实体，专门用于处理音频类型的片段
 * 支持本地音频文件和在线音频/播客内容
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/voice.ts
 * - /youapp/src/lib/app/snip/types.ts (VoiceVOSchema)
 */

import { LanguageEnum } from '@repo/common';
import { Author } from '@/common/types/snip.types';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { ReaderHTMLContent } from '../value-objects/content.vo';
import { WebpageMeta } from '../value-objects/webpage-meta.vo';
import {
  BoardItemInfo,
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { UploadFileMeta } from './type';
import { UnknownWebpage } from './unknown-webpage.entity';

// Hero 图片元数据（存储在 extra 字段中）
export interface HeroImageMetadata {
  blurhash?: string;
  average?: { r: number; g: number; b: number };
  width?: number;
  height?: number;
}

// Voice 创建参数 - 在线音频
export interface CreateOnlineVoiceParam extends CreateSnipParam {
  webpage: WebpageMeta;
  showNotes: ReaderHTMLContent;
  playUrl?: string;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
}

// Voice 创建参数 - 本地音频文件
export interface CreateLocalVoiceParam extends CreateSnipParam {
  file: UploadFileMeta;
  heroImageUrl?: string;
  authors?: Author[];
  publishedAt?: Date;
  playUrl?: string;
}

// Voice 创建参数的联合类型
export type CreateVoiceParam = CreateOnlineVoiceParam | CreateLocalVoiceParam;

// Voice 更新参数
export interface UpdateVoiceParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  webpage?: WebpageMeta;
  showNotes?: ReaderHTMLContent;
  playUrl?: string;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
}

// 类型守卫函数
function isOnlineVoiceParam(param: CreateVoiceParam): param is CreateOnlineVoiceParam {
  return 'webpage' in param;
}

function isLocalVoiceParam(param: CreateVoiceParam): param is CreateLocalVoiceParam {
  return 'file' in param;
}

export class Voice extends Snip {
  // Voice 特有字段
  public playUrl?: string;
  public showNotes?: ReaderHTMLContent;
  public file?: UploadFileMeta;
  public webpage?: WebpageMeta;
  public publishedAt?: Date;
  public heroImageUrl?: string;
  public authors?: Author[];

  constructor(param: {
    id?: string;
    createdAt?: Date;
    updatedAt?: Date;
    title?: string;
    spaceId: string;
    creatorId: string;
    parentId?: string;
    extra?: string;
    status?: SnipStatus;
    visibility?: VisibilityType;
    boardId?: string;
    boardItemInfo?: BoardItemInfo;
    position?: BoardPositionInfo;
    // Voice 特有字段
    playUrl?: string;
    showNotes?: ReaderHTMLContent;
    file?: UploadFileMeta;
    webpage?: WebpageMeta;
    publishedAt?: Date;
    heroImageUrl?: string;
    authors?: Author[];
  }) {
    super(
      param.id || '',
      param.createdAt || new Date(),
      param.updatedAt || new Date(),
      param.creatorId,
      param.spaceId,
      param.parentId,
      param.title || param.webpage?.getTitle() || param.file?.name || '',
      param.extra,
      SnipType.VOICE,
      param.file ? SnipFrom.FILE : SnipFrom.WEBPAGE,
      param.status,
      param.visibility || 'private',
      param.boardId,
      param.boardItemInfo,
      param.position,
    );

    this.playUrl = param.playUrl;
    this.showNotes = param.showNotes;
    this.file = param.file;
    this.webpage = param.webpage;
    this.publishedAt = param.publishedAt;
    this.heroImageUrl = param.heroImageUrl;
    this.authors = param.authors;
  }

  static async create(
    param: CreateVoiceParam,
    boardPositionService?: BoardPositionDomainService,
  ): Promise<Voice> {
    let title: string;
    let from: SnipFrom;

    // 根据参数类型确定标题和来源
    if (isOnlineVoiceParam(param)) {
      title = param.title || param.webpage.getTitle();
      from = SnipFrom.WEBPAGE;
    } else if (isLocalVoiceParam(param)) {
      title = param.title || param.file.name;
      from = SnipFrom.FILE;
    } else {
      throw new Error('Invalid voice creation parameters');
    }

    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.VOICE,
        from,
        title,
      },
      boardPositionService,
    );

    const voice = new Voice({
      id: snip.id,
      createdAt: snip.createdAt,
      updatedAt: snip.updatedAt,
      creatorId: snip.creatorId,
      spaceId: snip.spaceId,
      parentId: snip.parentId,
      title: snip.title,
      extra: snip.extra,
      status: snip.status,
      visibility: snip.visibility,
      boardId: snip.boardId,
      playUrl: isOnlineVoiceParam(param) ? param.playUrl : undefined,
      showNotes: isOnlineVoiceParam(param) ? param.showNotes : undefined,
      file: isLocalVoiceParam(param) ? param.file : undefined,
      webpage: isOnlineVoiceParam(param) ? param.webpage : undefined,
      publishedAt: isOnlineVoiceParam(param) ? param.publishedAt : undefined,
      heroImageUrl: param.heroImageUrl,
      authors: isOnlineVoiceParam(param) ? param.authors : undefined,
      position: snip.position,
    });

    voice.isNew = true;
    return voice;
  }

  /**
   * 创建本地语音文件
   */
  static async createLocalVoice(
    param: CreateLocalVoiceParam & {
      file: UploadFileMeta;
    },
    boardPositionService?: any,
  ): Promise<Voice> {
    const title = param.title || param.file.name;

    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.VOICE,
        from: SnipFrom.FILE,
        title,
      },
      boardPositionService,
    );

    const voice = new Voice({
      id: snip.id,
      createdAt: snip.createdAt,
      updatedAt: snip.updatedAt,
      creatorId: snip.creatorId,
      spaceId: snip.spaceId,
      parentId: snip.parentId,
      title: snip.title,
      extra: snip.extra,
      status: snip.status,
      visibility: snip.visibility,
      boardId: snip.boardId,
      file: param.file,
      heroImageUrl: param.heroImageUrl,
      authors: param.authors,
      publishedAt: param.publishedAt,
      playUrl: param.playUrl,
      position: snip.position,
    });

    voice.isNew = true;
    return voice;
  }

  /**
   * 创建在线语音（播客等）
   */
  static async createOnlineVoice(
    param: CreateOnlineVoiceParam,
    boardPositionService?: any,
  ): Promise<Voice> {
    const title = param.title || param.webpage.getTitle();

    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.VOICE,
        from: SnipFrom.WEBPAGE,
        title,
      },
      boardPositionService,
    );

    const voice = new Voice({
      id: snip.id,
      createdAt: snip.createdAt,
      updatedAt: snip.updatedAt,
      creatorId: snip.creatorId,
      spaceId: snip.spaceId,
      parentId: snip.parentId,
      title: snip.title,
      extra: snip.extra,
      status: snip.status,
      visibility: snip.visibility,
      boardId: snip.boardId,
      playUrl: param.playUrl,
      showNotes: param.showNotes,
      webpage: param.webpage,
      publishedAt: param.publishedAt,
      heroImageUrl: param.heroImageUrl,
      authors: param.authors,
      position: snip.position,
    });

    voice.isNew = true;
    return voice;
  }

  /**
   * 更新音频信息
   */
  update(param: UpdateVoiceParam): void {
    // 更新基础 Snip 字段
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });

    // 更新 Voice 特有字段
    if (param.webpage !== undefined) {
      this.webpage = param.webpage;
    }

    if (param.showNotes !== undefined) {
      this.showNotes = param.showNotes;
    }

    if (param.playUrl !== undefined) {
      this.playUrl = param.playUrl;
    }

    if (param.publishedAt !== undefined) {
      this.publishedAt = param.publishedAt;
    }

    if (param.heroImageUrl !== undefined) {
      this.heroImageUrl = param.heroImageUrl;
    }

    if (param.authors !== undefined) {
      this.authors = param.authors;
    }
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !this.status && !!this.heroImageUrl;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    return this.heroImageUrl;
  }

  /**
   * 从音频 show notes 中提取所有图片 URL
   * 包括 Hero 图片和作者头像
   */
  extractImageUrls(): string[] {
    let imageUrls: string[] = [];

    // 使用 ReaderHTMLContent 的方法提取图片 URL
    if (this.showNotes instanceof ReaderHTMLContent) {
      imageUrls = this.showNotes.extractImageUrls();
    }

    // 添加 Hero 图片
    if (this.heroImageUrl) {
      imageUrls.push(this.heroImageUrl);
    }

    // 添加作者头像
    if (this.authors) {
      this.authors.forEach((author) => {
        if (author.picture) {
          try {
            new URL(author.picture); // 验证 URL 格式
            imageUrls.push(author.picture);
          } catch {
            // 忽略无效的 URL
          }
        }
      });
    }

    // 去重并返回
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 替换 show notes 中的图片 URL
      if (this.showNotes instanceof ReaderHTMLContent) {
        this.showNotes = this.showNotes.replaceImageUrls([
          { originalUrl: originalUrl, url: newUrl },
        ]);
      }

      // 更新 Hero 图片 URL
      if (this.heroImageUrl === originalUrl) {
        this.heroImageUrl = newUrl;

        // 更新 Hero 图片元数据
        const heroImageMetadata: HeroImageMetadata = {};
        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash as string;
        }
        if (image.width) {
          heroImageMetadata.width = image.width as number;
        }
        if (image.height) {
          heroImageMetadata.height = image.height as number;
        }
        if (image.average) {
          heroImageMetadata.average = image.average as { r: number; g: number; b: number };
        }

        // 更新 extra 字段中的元数据
        try {
          const extraData = this.extra ? SafeParse(this.extra, false, {}) : {};
          (extraData as any).hero_image_metadata = heroImageMetadata;
          this.extra = JSON.stringify(extraData);
        } catch {
          // 如果解析失败，创建新的 extra 数据
          this.extra = JSON.stringify({ hero_image_metadata: heroImageMetadata });
        }
      }

      // 更新作者头像 URL
      if (this.authors) {
        this.authors.forEach((author) => {
          if (author.picture === originalUrl) {
            author.picture = newUrl;
          }
        });
      }
    });
  }

  /**
   * 检查是否是本地音频文件
   */
  isLocalFile(): boolean {
    return !!this.file && this.from === SnipFrom.FILE;
  }

  /**
   * 检查是否是在线音频（播客等）
   */
  isOnlineAudio(): boolean {
    return !!this.webpage && this.from === SnipFrom.WEBPAGE;
  }

  /**
   * 检查是否有有效的播放 URL
   */
  hasPlayUrl(): boolean {
    return !!this.playUrl;
  }

  /**
   * 获取音频的显示标题
   */
  getDisplayTitle(): string {
    if (this.title) {
      return this.title;
    }

    if (this.webpage) {
      return this.webpage.getTitle();
    }

    if (this.file) {
      return this.file.name;
    }

    return 'Untitled Voice';
  }

  /**
   * 获取音频的显示描述
   */
  getDisplayDescription(): string {
    if (this.webpage) {
      return this.webpage.getDescription();
    }

    if (this.showNotes) {
      return this.showNotes.getPlain()?.substring(0, 200) || '';
    }

    return '';
  }

  /**
   * 获取音频的语言
   */
  getLanguage(): LanguageEnum | undefined {
    return this.showNotes?.getLanguage();
  }

  /**
   * 获取音频的文件大小（仅适用于本地文件）
   */
  getFileSize(): number | undefined {
    return this.file?.size;
  }

  /**
   * 获取音频的 MIME 类型（仅适用于本地文件）
   */
  getMimeType(): string | undefined {
    return this.file?.mimeType;
  }

  /**
   * 获取 Hero 图片元数据
   */
  getHeroImageMetadata(): HeroImageMetadata | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.hero_image_metadata;
  }

  /**
   * 获取音频平台信息（从 webpage 中推断）
   */
  getAudioPlatform(): string | undefined {
    if (!this.webpage) {
      return undefined;
    }

    const host = this.webpage.getSite().host;
    if (host.includes('spotify.com')) {
      return 'Spotify';
    }
    if (host.includes('apple.com')) {
      return 'Apple Podcasts';
    }
    if (host.includes('soundcloud.com')) {
      return 'SoundCloud';
    }
    if (host.includes('anchor.fm')) {
      return 'Anchor';
    }
    if (host.includes('overcast.fm')) {
      return 'Overcast';
    }
    return undefined;
  }

  /**
   * 检查是否有 show notes
   */
  hasShowNotes(): boolean {
    return !!this.showNotes && !this.showNotes.isEmpty();
  }

  /**
   * 检查是否有作者信息
   */
  hasAuthors(): boolean {
    return !!this.authors && this.authors.length > 0;
  }

  /**
   * 获取第一个作者（主要作者）
   */
  getPrimaryAuthor(): Author | undefined {
    return this.authors?.[0];
  }

  /**
   * 从 UnknownWebpage 创建 Voice 实体
   * 模仿 youapp 的 Voice.ofUnknownWebpage 方法
   */
  static ofUnknownWebpage(
    unknownWebpage: UnknownWebpage,
    param: {
      webpage: WebpageMeta;
      playUrl?: string;
      title: string;
      heroImageUrl?: string;
      showNotes: ReaderHTMLContent;
      authors?: Author[];
      publishedAt?: Date;
      extra?: string;
      visibility?: VisibilityType;
    },
  ): Voice {
    return new Voice({
      id: unknownWebpage.id,
      createdAt: unknownWebpage.createdAt,
      updatedAt: unknownWebpage.updatedAt,
      title: param.title,
      spaceId: unknownWebpage.spaceId,
      creatorId: unknownWebpage.creatorId,
      parentId: unknownWebpage.parentId,
      extra: param.extra || unknownWebpage.extra,
      status: undefined, // 重置状态为 null
      visibility: param.visibility || unknownWebpage.visibility,
      boardId: unknownWebpage.boardId,
      boardItemInfo: undefined,
      position: unknownWebpage.position,
      // Voice 特有字段
      playUrl: param.playUrl,
      showNotes: param.showNotes,
      webpage: param.webpage,
      publishedAt: param.publishedAt,
      heroImageUrl: param.heroImageUrl,
      authors: param.authors,
    });
  }
}
