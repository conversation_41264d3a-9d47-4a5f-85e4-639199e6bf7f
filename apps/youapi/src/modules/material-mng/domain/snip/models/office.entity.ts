/**
 * Office Entity - Office 领域实体
 * Office文档实体，继承自 Snip 基础实体，专门用于处理 Office 文档类型的片段
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/office.ts
 * - /youapp/src/lib/app/snip/types.ts (OfficeVOSchema)
 */
import { ContentFormatEnum, LanguageEnum } from '@repo/common';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { ReaderHTMLContent } from '../value-objects/content.vo';
import {
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { Author, UploadFileMeta } from './type';

// Office 特有的内容格式（与 value object 保持一致）
export interface ReaderHTMLContentData {
  format: ContentFormatEnum.READER_HTML;
  raw: string;
  plain?: string;
  language?: LanguageEnum;
}

// Hero 图片元数据（存储在 extra 字段中）
export interface HeroImageMetadata {
  blurhash?: string;
  average?: { r: number; g: number; b: number };
  width?: number;
  height?: number;
}

// Office 创建参数
export interface CreateOfficeParam extends CreateSnipParam {
  file: UploadFileMeta;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
  content?: ReaderHTMLContent;
}

// Office 更新参数
export interface UpdateOfficeParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  file?: UploadFileMeta;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
  content?: ReaderHTMLContent;
}

export class Office extends Snip {
  // Office 特有字段
  public file: UploadFileMeta;
  public publishedAt?: Date;
  public heroImageUrl?: string;
  public authors?: Author[];
  public content?: ReaderHTMLContent;

  constructor(
    // 基础 Snip 字段
    id: string,
    createdAt: Date,
    updatedAt: Date,
    creatorId: string,
    spaceId: string,
    parentId: string | undefined,
    title: string,
    extra: string | undefined,
    status: SnipStatus | undefined,
    visibility: VisibilityType,
    // Office 特有字段（必需的）
    file: UploadFileMeta,
    // 可选字段
    boardId?: string,
    publishedAt?: Date,
    heroImageUrl?: string,
    authors?: Author[],
    content?: ReaderHTMLContent,
    position?: BoardPositionInfo,
  ) {
    super(
      id,
      createdAt,
      updatedAt,
      creatorId,
      spaceId,
      parentId,
      title,
      extra,
      SnipType.OFFICE,
      SnipFrom.FILE,
      status,
      visibility,
      boardId,
      undefined,
      position,
    );

    this.file = file;
    this.publishedAt = publishedAt;
    this.heroImageUrl = heroImageUrl;
    this.authors = authors;
    this.content = content;
  }

  static async create(
    param: CreateOfficeParam,
    boardPositionService?: BoardPositionDomainService,
  ): Promise<Office> {
    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.OFFICE,
        from: SnipFrom.FILE,
        title: param.title || param.file.name,
      },
      boardPositionService,
    );

    const office = new Office(
      snip.id,
      snip.createdAt,
      snip.updatedAt,
      snip.creatorId,
      snip.spaceId,
      snip.parentId,
      snip.title,
      snip.extra,
      snip.status,
      snip.visibility,
      param.file,
      snip.boardId,
      param.publishedAt,
      param.heroImageUrl,
      param.authors,
      param.content,
      snip.position,
    );

    office.isNew = true;
    return office;
  }

  /**
   * 更新 Office 文档信息
   */
  updateOffice(param: UpdateOfficeParam): void {
    // 更新基础 Snip 字段
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });

    // 更新 Office 特有字段
    if (param.file !== undefined) {
      this.file = param.file;
    }

    if (param.publishedAt !== undefined) {
      this.publishedAt = param.publishedAt;
    }

    if (param.heroImageUrl !== undefined) {
      this.heroImageUrl = param.heroImageUrl;
    }

    if (param.authors !== undefined) {
      this.authors = param.authors;
    }

    if (param.content !== undefined) {
      this.content = param.content;
    }
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !!this.heroImageUrl;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    return this.heroImageUrl;
  }

  /**
   * 从 Office 文档中提取所有图片 URL
   * 包括 Hero 图片和作者头像
   */
  extractImageUrls(): string[] {
    let imageUrls: string[] = [];

    // 使用 ReaderHTMLContent 的方法提取图片 URL
    if (this.content instanceof ReaderHTMLContent) {
      imageUrls = this.content.extractImageUrls();
    }

    // 添加 Hero 图片
    if (this.heroImageUrl) {
      imageUrls.push(this.heroImageUrl);
    }

    // 添加作者头像
    if (this.authors) {
      this.authors.forEach((author) => {
        if (author.picture) {
          try {
            new URL(author.picture); // 验证 URL 格式
            imageUrls.push(author.picture);
          } catch {
            // 忽略无效的 URL
          }
        }
      });
    }

    // 去重并返回
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 替换内容中的图片 URL
      if (this.content instanceof ReaderHTMLContent) {
        this.content = this.content.replaceImageUrls([{ originalUrl: originalUrl, url: newUrl }]);
      }

      // 更新 Hero 图片 URL
      if (this.heroImageUrl === originalUrl) {
        this.heroImageUrl = newUrl;

        // 更新 Hero 图片元数据
        const heroImageMetadata: HeroImageMetadata = {};
        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash as string;
        }
        if (image.width) {
          heroImageMetadata.width = image.width as number;
        }
        if (image.height) {
          heroImageMetadata.height = image.height as number;
        }
        if (image.average) {
          heroImageMetadata.average = image.average as { r: number; g: number; b: number };
        }

        // 更新 extra 字段中的元数据
        const extraData = SafeParse(this.extra, false, {}) as any;
        extraData.heroImageMetadata = heroImageMetadata;
        this.extra = JSON.stringify(extraData);
      }

      // 更新作者头像 URL
      if (this.authors) {
        this.authors.forEach((author) => {
          if (author.picture === originalUrl) {
            author.picture = newUrl;
          }
        });
      }
    });
  }

  /**
   * 获取文件显示名称
   */
  getDisplayName(): string {
    return this.title || this.file.name;
  }

  /**
   * 获取文件大小（字节）
   */
  getFileSize(): number {
    return this.file.size;
  }

  /**
   * 获取文件 MIME 类型
   */
  getMimeType(): string {
    return this.file.mimeType;
  }

  /**
   * 检查是否有发布日期
   */
  hasPublishedDate(): boolean {
    return !!this.publishedAt;
  }

  /**
   * 获取发布年份
   */
  getPublishedYear(): number | undefined {
    if (this.publishedAt) {
      return this.publishedAt.getFullYear();
    }
    return undefined;
  }

  /**
   * 获取文件语言
   */
  getLanguage(): LanguageEnum | undefined {
    return this.content?.getLanguage();
  }

  /**
   * 检查是否有内容
   */
  hasContent(): boolean {
    return !!this.content;
  }

  /**
   * 获取 Hero 图片元数据
   */
  getHeroImageMetadata(): HeroImageMetadata | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.heroImageMetadata;
  }
}
