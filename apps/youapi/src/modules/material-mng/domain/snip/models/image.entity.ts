/**
 * Image Entity - Image 领域实体
 * 图片实体，继承自 Snip 基础实体，专门用于处理图片类型的片段
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/image.ts
 * - /youapp/src/lib/app/snip/types.ts (ImageVOSchema)
 */

import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { WebpageMeta } from '../value-objects/webpage-meta.vo';
import {
  BoardItemInfo,
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { FileMeta, TransferredFileMeta } from './type';

// Hero 图片元数据（存储在 extra 字段中）
export interface HeroImageMetadata {
  blurhash?: string;
  average?: { r: number; g: number; b: number };
  width?: number;
  height?: number;
}

// Image 创建参数
export interface CreateImageParam extends CreateSnipParam {
  file: FileMeta;
  webpage?: WebpageMeta;
  description?: string;
  extractedText?: string;
}

// Image 更新参数
export interface UpdateImageParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  file?: FileMeta;
  description?: string;
  extractedText?: string;
}

export class Image extends Snip {
  // Image 特有字段
  public file: FileMeta;
  public readonly webpage?: WebpageMeta;
  public description?: string;
  public extractedText?: string;

  constructor(
    // 基础 Snip 字段
    id: string,
    createdAt: Date,
    updatedAt: Date,
    creatorId: string,
    spaceId: string,
    parentId: string | undefined,
    title: string,
    extra: string | undefined,
    status: SnipStatus | undefined,
    visibility: VisibilityType,
    // Image 特有字段（必需的）
    file: FileMeta,
    // 可选字段
    boardId?: string,
    boardItemInfo?: BoardItemInfo,
    position?: BoardPositionInfo,
    webpage?: WebpageMeta,
    description?: string,
    extractedText?: string,
  ) {
    super(
      id,
      createdAt,
      updatedAt,
      creatorId,
      spaceId,
      parentId,
      title,
      extra,
      SnipType.IMAGE,
      SnipFrom.FILE,
      status,
      visibility,
      boardId,
      boardItemInfo,
      position,
    );

    this.file = file;
    this.webpage = webpage;
    this.description = description;
    this.extractedText = extractedText;
  }

  static async create(
    param: CreateImageParam,
    boardPositionService?: BoardPositionDomainService,
  ): Promise<Image> {
    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.IMAGE,
        from: SnipFrom.FILE,
        title: param.title || Image.extractTitleFromFile(param.file),
      },
      boardPositionService,
    );

    const image = new Image(
      snip.id,
      snip.createdAt,
      snip.updatedAt,
      snip.creatorId,
      snip.spaceId,
      snip.parentId,
      snip.title,
      snip.extra,
      snip.status,
      snip.visibility,
      param.file,
      snip.boardId,
      undefined,
      snip.position,
      param.webpage,
      param.description,
      param.extractedText,
    );

    image.isNew = true;
    return image;
  }

  /**
   * 从文件元数据中提取标题
   */
  private static extractTitleFromFile(file: FileMeta): string {
    if ('name' in file && file.name) {
      // 移除文件扩展名
      return file.name.replace(/\.[^/.]+$/, '');
    }
    if ('originalUrl' in file && file.originalUrl) {
      // 从 URL 中提取文件名
      const url = new URL(file.originalUrl);
      const filename = url.pathname.split('/').pop() || 'Untitled Image';
      return filename.replace(/\.[^/.]+$/, '');
    }
    return 'Untitled Image';
  }

  /**
   * 更新图片信息
   */
  updateImage(param: UpdateImageParam): void {
    // 更新基础 Snip 字段
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });

    // 更新 Image 特有字段
    if (param.file !== undefined) {
      this.file = param.file;
    }

    if (param.description !== undefined) {
      this.description = param.description;
    }

    if (param.extractedText !== undefined) {
      this.extractedText = param.extractedText;
    }
  }

  /**
   * 检查是否为已转存文件
   */
  isTransferredFile(): boolean {
    return 'originalUrl' in this.file && 'storageUrl' in this.file;
  }

  /**
   * 检查是否为未转存文件
   */
  isUntransferredFile(): boolean {
    return 'originalUrl' in this.file && !('storageUrl' in this.file);
  }

  /**
   * 检查是否为上传文件
   */
  isUploadFile(): boolean {
    return 'storageUrl' in this.file && !('originalUrl' in this.file);
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !this.status && 'storageUrl' in this.file;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    if ('storageUrl' in this.file) {
      return this.file.storageUrl;
    }
    return undefined;
  }

  /**
   * 从图片中提取所有图片 URL
   * 主要返回图片自身的 URL
   */
  extractImageUrls(): string[] {
    const imageUrls: string[] = [];

    // 添加存储 URL（如果存在）
    if ('storageUrl' in this.file && this.file.storageUrl) {
      imageUrls.push(this.file.storageUrl);
    }

    // 添加原始 URL（如果存在且不同于存储 URL）
    if ('originalUrl' in this.file && this.file.originalUrl) {
      if (!imageUrls.includes(this.file.originalUrl)) {
        imageUrls.push(this.file.originalUrl);
      }
    }

    return imageUrls;
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 如果当前文件是未转存的，并且原始 URL 匹配，则更新为已转存状态
      if (
        this.isUntransferredFile() &&
        'originalUrl' in this.file &&
        this.file.originalUrl === originalUrl
      ) {
        // 构建转存后的文件元数据
        const transferredFile: TransferredFileMeta = {
          originalUrl: originalUrl,
          storageUrl: newUrl,
          name: (image.name as string) || this.extractFileNameFromUrl(originalUrl),
          mimeType: (image.mimeType as string) || 'image/jpeg',
          size: (image.size as number) || 0,
        };

        // 添加图片特有的元数据
        if (image.width) {
          transferredFile.width = image.width as number;
        }
        if (image.height) {
          transferredFile.height = image.height as number;
        }
        if (image.blurhash) {
          transferredFile.blurhash = image.blurhash as string;
        }
        if (image.average) {
          transferredFile.average = image.average as { r: number; g: number; b: number };
        }

        this.file = transferredFile;

        // 更新 Hero 图片元数据到 extra 字段
        const heroImageMetadata: HeroImageMetadata = {};
        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash as string;
        }
        if (image.width) {
          heroImageMetadata.width = image.width as number;
        }
        if (image.height) {
          heroImageMetadata.height = image.height as number;
        }
        if (image.average) {
          heroImageMetadata.average = image.average as { r: number; g: number; b: number };
        }

        // 更新 extra 字段中的元数据
        const extraData = SafeParse(this.extra, false, {}) as any;
        (extraData as any).heroImageMetadata = heroImageMetadata;
        this.extra = JSON.stringify(extraData);
      }
    });
  }

  /**
   * 从 URL 中提取文件名
   */
  private extractFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname.split('/').pop() || 'image';
    } catch {
      return 'image';
    }
  }

  /**
   * 获取聊天兼容的图片 URL
   */
  async getChatImageUrl(): Promise<string[]> {
    if (this.isUntransferredFile()) return [];
    // 优先返回存储 URL，因为它更稳定
    if ('storageUrl' in this.file && this.file.storageUrl) {
      return [this.file.storageUrl];
    }

    return [];
  }

  /**
   * 获取图片的显示标题
   */
  getDisplayTitle(): string {
    if (this.title) {
      return this.title;
    }

    return Image.extractTitleFromFile(this.file);
  }

  /**
   * 获取图片的显示描述
   */
  getDisplayDescription(): string {
    if (this.description) {
      return this.description;
    }

    if (this.extractedText) {
      return this.extractedText.substring(0, 200);
    }

    if (this.webpage) {
      return this.webpage.getDescription();
    }

    return '';
  }

  /**
   * 获取图片尺寸
   */
  getDimensions(): { width?: number; height?: number } {
    if (this.isTransferredFile()) {
      const file = this.file as TransferredFileMeta;
      return {
        width: file.width,
        height: file.height,
      };
    }
    return {};
  }

  /**
   * 获取文件大小
   */
  getFileSize(): number | undefined {
    if ('size' in this.file) {
      return this.file.size;
    }
    return undefined;
  }

  /**
   * 获取 MIME 类型
   */
  getMimeType(): string | undefined {
    if ('mimeType' in this.file) {
      return this.file.mimeType;
    }
    return undefined;
  }

  /**
   * 检查是否有提取的文本内容
   */
  hasExtractedText(): boolean {
    return !!this.extractedText && this.extractedText.length > 0;
  }

  /**
   * 获取 Hero 图片元数据
   */
  getHeroImageMetadata(): HeroImageMetadata | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.heroImageMetadata;
  }
}
