/**
 * Article Entity - Article 领域实体
 * 文章实体，继承自 Snip 基础实体，专门用于处理网页文章类型的片段
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/article.ts
 * - /youapp/src/lib/app/snip/types.ts (ArticleVOSchema)
 */

import { ContentFormatEnum, LanguageEnum } from '@repo/common';
import { Author } from '@/common/types/snip.types';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { ReaderHTMLContent } from '../value-objects/content.vo';
import { WebpageMeta } from '../value-objects/webpage-meta.vo';
import {
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { UnknownWebpage } from './unknown-webpage.entity';

// Article 特有的内容格式（与 value object 保持一致）
export interface ReaderHTMLContentData {
  format: ContentFormatEnum.READER_HTML;
  raw: string;
  plain?: string;
  language?: LanguageEnum;
}

// Hero 图片元数据（存储在 extra 字段中）
export interface HeroImageMetadata {
  blurhash?: string;
  average?: { r: number; g: number; b: number };
  width?: number;
  height?: number;
}

// Article 创建参数
export interface CreateArticleParam extends CreateSnipParam {
  webpage?: WebpageMeta;
  content: ReaderHTMLContent;
  publishedAt?: Date;
  playUrl?: string;
  heroImageUrl?: string;
  authors?: Author[];
}

// Article 更新参数
export interface UpdateArticleParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  webpage?: WebpageMeta;
  content?: ReaderHTMLContent;
  publishedAt?: Date;
  playUrl?: string;
  heroImageUrl?: string;
  authors?: Author[];
}

export class Article extends Snip {
  // Article 特有字段
  public webpage?: WebpageMeta;
  public content: ReaderHTMLContent;
  public publishedAt?: Date;
  public playUrl?: string;
  public heroImageUrl?: string;
  public authors?: Author[];
  public boardPositionService?: BoardPositionDomainService;

  constructor(
    // 基础 Snip 字段
    id: string,
    createdAt: Date,
    updatedAt: Date,
    creatorId: string,
    spaceId: string,
    parentId: string | undefined,
    title: string,
    extra: string | undefined,
    status: SnipStatus | undefined,
    visibility: VisibilityType,
    // Article 特有字段
    content: ReaderHTMLContent,
    // 可选字段
    boardId?: string,
    webpage?: WebpageMeta,
    publishedAt?: Date,
    playUrl?: string,
    heroImageUrl?: string,
    authors?: Author[],
    position?: BoardPositionInfo,
  ) {
    super(
      id,
      createdAt,
      updatedAt,
      creatorId,
      spaceId,
      parentId,
      title,
      extra,
      SnipType.ARTICLE,
      SnipFrom.WEBPAGE,
      status,
      visibility,
      boardId,
      undefined,
      position,
    );

    this.webpage = webpage;
    this.content = content;
    this.publishedAt = publishedAt;
    this.playUrl = playUrl;
    this.heroImageUrl = heroImageUrl;
    this.authors = authors;
  }

  static async create(
    param: CreateArticleParam,
    boardPositionService?: BoardPositionDomainService,
  ): Promise<Article> {
    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.ARTICLE,
        from: SnipFrom.WEBPAGE,
        title: param.title || param.webpage?.getTitle() || '',
      },
      boardPositionService,
    );

    const article = new Article(
      snip.id,
      snip.createdAt,
      snip.updatedAt,
      snip.creatorId,
      snip.spaceId,
      snip.parentId,
      snip.title,
      snip.extra,
      snip.status,
      snip.visibility,
      param.content,
      snip.boardId,
      param.webpage,
      param.publishedAt,
      param.playUrl,
      param.heroImageUrl,
      param.authors,
      snip.position,
    );

    article.isNew = true;
    return article;
  }

  /**
   * 更新文章信息
   */
  update(param: UpdateArticleParam): void {
    // 更新 Article 特有字段
    if (param.webpage !== undefined) {
      this.webpage = param.webpage;
    }

    if (param.content !== undefined) {
      this.content = param.content;
    }

    if (param.publishedAt !== undefined) {
      this.publishedAt = param.publishedAt;
    }

    if (param.playUrl !== undefined) {
      this.playUrl = param.playUrl;
    }

    if (param.heroImageUrl !== undefined) {
      this.heroImageUrl = param.heroImageUrl;
    }

    if (param.authors !== undefined) {
      this.authors = param.authors;
    }

    // 最后调用 super.update 触发领域事件
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !this.status && !!this.heroImageUrl;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    return this.heroImageUrl;
  }

  /**
   * 从文章内容中提取所有图片 URL
   * 包括 Hero 图片和作者头像
   */
  extractImageUrls(): string[] {
    let imageUrls: string[] = [];

    // 使用 ReaderHTMLContent 的方法提取图片 URL
    if (this.content instanceof ReaderHTMLContent) {
      imageUrls = this.content.extractImageUrls();
    }

    // 添加 Hero 图片
    if (this.heroImageUrl) {
      imageUrls.push(this.heroImageUrl);
    }

    // 添加作者头像
    if (this.authors) {
      this.authors.forEach((author) => {
        if (author.picture) {
          try {
            new URL(author.picture); // 验证 URL 格式
            imageUrls.push(author.picture);
          } catch {
            // 忽略无效的 URL
          }
        }
      });
    }

    // 去重并返回
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 替换内容中的图片 URL
      if (this.content instanceof ReaderHTMLContent) {
        this.content = this.content.replaceImageUrls([{ originalUrl: originalUrl, url: newUrl }]);
      }

      // 更新 Hero 图片 URL
      if (this.heroImageUrl === originalUrl) {
        this.heroImageUrl = newUrl;

        // 更新 Hero 图片元数据
        const heroImageMetadata: HeroImageMetadata = {};
        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash as string;
        }
        if (image.width) {
          heroImageMetadata.width = image.width as number;
        }
        if (image.height) {
          heroImageMetadata.height = image.height as number;
        }
        if (image.average) {
          heroImageMetadata.average = image.average as { r: number; g: number; b: number };
        }

        // 更新 extra 字段中的元数据
        try {
          const extraData = this.extra ? SafeParse(this.extra, false, {}) : {};
          (extraData as any).heroImageMetadata = heroImageMetadata;
          this.extra = JSON.stringify(extraData);
        } catch {
          // 如果解析失败，创建新的 extra 数据
          this.extra = JSON.stringify({ heroImageMetadata: heroImageMetadata });
        }
      }

      // 更新作者头像 URL
      if (this.authors) {
        this.authors.forEach((author) => {
          if (author.picture === originalUrl) {
            author.picture = newUrl;
          }
        });
      }
    });
  }

  /**
   * 获取文章的显示标题
   */
  getDisplayTitle(): string {
    return this.title || this.webpage?.getTitle() || '';
  }

  /**
   * 获取文章的显示描述
   */
  getDisplayDescription(): string {
    return this.webpage?.getDescription() || this.content.getPlain()?.substring(0, 200) || '';
  }

  /**
   * 检查文章是否有有效的播放 URL
   */
  hasPlayUrl(): boolean {
    return !!this.playUrl;
  }

  /**
   * 获取文章的语言
   */
  getLanguage(): LanguageEnum | undefined {
    return this.content.getLanguage();
  }

  /**
   * 获取 Hero 图片元数据
   */
  getHeroImageMetadata(): HeroImageMetadata | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.heroImageMetadata;
  }

  /**
   * 从 UnknownWebpage 创建 Article 实体
   * 模仿 youapp 的 Article.ofUnknownWebpage 方法
   */
  static ofUnknownWebpage(
    unknownWebpage: UnknownWebpage,
    param: {
      webpage: WebpageMeta;
      title: string;
      content: ReaderHTMLContent;
      authors?: Author[];
      heroImageUrl?: string;
      publishedAt?: Date;
      extra?: string;
      visibility?: VisibilityType;
      playUrl?: string;
    },
  ): Article {
    return new Article(
      unknownWebpage.id,
      unknownWebpage.createdAt,
      unknownWebpage.updatedAt,
      unknownWebpage.creatorId,
      unknownWebpage.spaceId,
      unknownWebpage.parentId,
      param.title,
      param.extra || unknownWebpage.extra,
      undefined, // 重置状态为 null
      param.visibility || unknownWebpage.visibility,
      param.content,
      unknownWebpage.boardId,
      param.webpage,
      param.publishedAt,
      param.playUrl,
      param.heroImageUrl,
      param.authors,
      unknownWebpage.position,
    );
  }
}
