/**
 * PDF Entity - PDF 领域实体
 * PDF文档实体，继承自 Snip 基础实体，专门用于处理PDF文档类型的片段
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/pdf.ts
 * - /youapp/src/lib/app/snip/types.ts (PDFVOSchema)
 */

import { LanguageEnum } from '@repo/common';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { ReaderHTMLContent } from '../value-objects/content.vo';
import {
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { Author, UploadFileMeta } from './type';

// PDF 创建参数
export interface CreatePDFParam extends CreateSnipParam {
  file: UploadFileMeta;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
  content?: ReaderHTMLContent;
}

// PDF 更新参数
export interface UpdatePDFParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  file?: UploadFileMeta;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
  content?: ReaderHTMLContent;
}

export class PDF extends Snip {
  // PDF 特有字段
  public file: UploadFileMeta;
  public publishedAt?: Date;
  public heroImageUrl?: string;
  public authors?: Author[];
  public content?: ReaderHTMLContent;

  constructor(
    // 基础 Snip 字段
    id: string,
    createdAt: Date,
    updatedAt: Date,
    creatorId: string,
    spaceId: string,
    parentId: string | undefined,
    title: string,
    extra: string | undefined,
    status: SnipStatus | undefined,
    visibility: VisibilityType,
    // PDF 特有字段（必需的）
    file: UploadFileMeta,
    // 可选字段
    boardId?: string,
    boardItemInfo?: any,
    position?: BoardPositionInfo,
    publishedAt?: Date,
    heroImageUrl?: string,
    authors?: Author[],
    content?: ReaderHTMLContent,
  ) {
    super(
      id,
      createdAt,
      updatedAt,
      creatorId,
      spaceId,
      parentId,
      title,
      extra,
      SnipType.PDF,
      SnipFrom.FILE,
      status,
      visibility,
      boardId,
      boardItemInfo,
      position,
    );

    this.file = file;
    this.publishedAt = publishedAt;
    this.heroImageUrl = heroImageUrl;
    this.authors = authors;
    this.content = content;
  }

  static async create(
    param: CreatePDFParam,
    positionService?: BoardPositionDomainService,
  ): Promise<PDF> {
    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.PDF,
        from: SnipFrom.FILE,
        title: param.title || PDF.extractTitleFromFile(param.file),
      },
      positionService,
    );

    const pdf = new PDF(
      snip.id,
      snip.createdAt,
      snip.updatedAt,
      snip.creatorId,
      snip.spaceId,
      snip.parentId,
      snip.title,
      snip.extra,
      snip.status,
      snip.visibility,
      param.file,
      snip.boardId,
      undefined,
      snip.position,
      param.publishedAt,
      param.heroImageUrl,
      param.authors,
      param.content,
    );

    pdf.isNew = true;
    return pdf;
  }

  /**
   * 从文件元数据中提取标题
   */
  private static extractTitleFromFile(file: UploadFileMeta): string {
    if (file.name) {
      // 移除文件扩展名
      return file.name.replace(/\.[^/.]+$/, '');
    }
    return 'PDF Document';
  }

  /**
   * 更新PDF文档信息
   */
  update(param: UpdatePDFParam): void {
    // 更新基础 Snip 字段
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });

    // 更新 PDF 特有字段
    if (param.file !== undefined) {
      this.file = param.file;
    }

    if (param.publishedAt !== undefined) {
      this.publishedAt = param.publishedAt;
    }

    if (param.heroImageUrl !== undefined) {
      this.heroImageUrl = param.heroImageUrl;
    }

    if (param.authors !== undefined) {
      this.authors = param.authors;
    }

    if (param.content !== undefined) {
      this.content = param.content;
    }
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !!this.heroImageUrl;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    return this.heroImageUrl;
  }

  /**
   * 从PDF文档中提取所有图片 URL
   * 包括 Hero 图片、内容中的图片和作者头像
   */
  extractImageUrls(): string[] {
    let imageUrls: string[] = [];

    // 使用 ReaderHTMLContent 的方法提取图片 URL
    if (this.content instanceof ReaderHTMLContent) {
      imageUrls = this.content.extractImageUrls();
    }

    // 添加 Hero 图片
    if (this.heroImageUrl) {
      imageUrls.push(this.heroImageUrl);
    }

    // 添加作者头像
    if (this.authors) {
      this.authors.forEach((author) => {
        if (author.picture) {
          try {
            new URL(author.picture); // 验证 URL 格式
            imageUrls.push(author.picture);
          } catch {
            // 忽略无效的 URL
          }
        }
      });
    }

    // 去重并返回
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 替换内容中的图片 URL
      if (this.content instanceof ReaderHTMLContent) {
        this.content = this.content.replaceImageUrls([{ originalUrl: originalUrl, url: newUrl }]);
      }

      // 更新 Hero 图片 URL
      if (this.heroImageUrl === originalUrl) {
        this.heroImageUrl = newUrl;
      }

      // 更新作者头像 URL
      if (this.authors) {
        this.authors.forEach((author) => {
          if (author.picture === originalUrl) {
            author.picture = newUrl;
          }
        });
      }
    });
  }

  /**
   * 获取PDF文档的显示标题
   */
  getDisplayTitle(): string {
    return this.title || PDF.extractTitleFromFile(this.file);
  }

  /**
   * 获取PDF文档的显示描述
   */
  getDisplayDescription(): string {
    if (this.content) {
      return this.content.getPlain()?.substring(0, 200) || '';
    }
    return '';
  }

  /**
   * 检查是否有内容
   */
  hasContent(): boolean {
    return !!this.content && !this.content.isEmpty();
  }

  /**
   * 获取PDF文档的语言
   */
  getLanguage(): LanguageEnum | undefined {
    return this.content?.getLanguage();
  }

  /**
   * 获取文件大小
   */
  getFileSize(): number {
    return this.file.size;
  }

  /**
   * 获取 MIME 类型
   */
  getMimeType(): string {
    return this.file.mimeType;
  }

  /**
   * 获取文件名
   */
  getFileName(): string {
    return this.file.name;
  }

  /**
   * 获取存储 URL
   */
  getStorageUrl(): string {
    return this.file.storageUrl;
  }

  /**
   * 检查是否为 PDF 文件
   */
  isPDFFile(): boolean {
    return (
      this.file.mimeType === 'application/pdf' || this.file.name.toLowerCase().endsWith('.pdf')
    );
  }

  /**
   * 检查文档是否已处理完成
   */
  isProcessed(): boolean {
    return !!this.content && !this.status;
  }

  /**
   * 获取可搜索的文本内容
   */
  getSearchableText(): string {
    if (this.content) {
      return this.content.getSearchableText();
    }
    return this.title || '';
  }

  /**
   * 获取文档页数（如果可用）
   */
  getPageCount(): number | undefined {
    // 页数信息可能存储在 extra 字段中
    if (this.extra) {
      const extraData = SafeParse(this.extra, false, {}) as any;
      return extraData.pageCount;
    }
    return undefined;
  }

  /**
   * 检查是否有作者信息
   */
  hasAuthors(): boolean {
    return !!this.authors && this.authors.length > 0;
  }

  /**
   * 获取主要作者
   */
  getPrimaryAuthor(): Author | undefined {
    if (this.authors && this.authors.length > 0) {
      return this.authors[0];
    }
    return undefined;
  }

  /**
   * 检查是否已发布
   */
  isPublished(): boolean {
    return !!this.publishedAt;
  }

  /**
   * 获取发布年份
   */
  getPublishedYear(): number | undefined {
    if (this.publishedAt) {
      return this.publishedAt.getFullYear();
    }
    return undefined;
  }

  /**
   * 获取文档元数据摘要
   */
  getMetadataSummary(): {
    title: string;
    size: number;
    pageCount?: number;
    authors?: string[];
    publishedYear?: number;
    language?: LanguageEnum;
  } {
    return {
      title: this.getDisplayTitle(),
      size: this.getFileSize(),
      pageCount: this.getPageCount(),
      authors: this.authors?.map((author) => author.name),
      publishedYear: this.getPublishedYear(),
      language: this.getLanguage(),
    };
  }
}
