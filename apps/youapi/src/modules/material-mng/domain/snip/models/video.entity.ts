/**
 * Video Entity - Video 领域实体
 * 视频实体，继承自 Snip 基础实体，专门用于处理视频类型的片段
 *
 * Migrated from:
 * - /youapp/src/lib/domain/snip/entity/video.ts
 * - /youapp/src/lib/app/snip/types.ts (VideoVOSchema)
 */

import { ContentFormatEnum, LanguageEnum } from '@repo/common';
import { Author } from '@/common/types/snip.types';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { ReaderHTMLContent } from '../value-objects/content.vo';
import { WebpageMeta } from '../value-objects/webpage-meta.vo';
import {
  CreateSnipParam,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
  VisibilityType,
} from './snip.entity';
import { UnknownWebpage } from './unknown-webpage.entity';

// Video 特有的内容格式（与 value object 保持一致）
export interface ReaderHTMLContentData {
  format: ContentFormatEnum.READER_HTML;
  raw: string;
  plain?: string;
  language?: LanguageEnum;
}

// Hero 图片元数据（存储在 extra 字段中）
export interface HeroImageMetadata {
  blurhash?: string;
  average?: { r: number; g: number; b: number };
  width?: number;
  height?: number;
}

// Video 创建参数
export interface CreateVideoParam extends CreateSnipParam {
  webpage: WebpageMeta;
  description: ReaderHTMLContent;
  playUrl?: string;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
}

// Video 更新参数
export interface UpdateVideoParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
  webpage?: WebpageMeta;
  description?: ReaderHTMLContent;
  playUrl?: string;
  publishedAt?: Date;
  heroImageUrl?: string;
  authors?: Author[];
}

export class Video extends Snip {
  // Video 特有字段
  public webpage: WebpageMeta;
  public description: ReaderHTMLContent;
  public playUrl?: string;
  public publishedAt?: Date;
  public heroImageUrl?: string;
  public authors?: Author[];

  constructor(
    // 基础 Snip 字段
    id: string,
    createdAt: Date,
    updatedAt: Date,
    creatorId: string,
    spaceId: string,
    parentId: string | undefined,
    title: string,
    extra: string | undefined,
    status: SnipStatus | undefined,
    visibility: VisibilityType,
    // Video 特有字段（必需的）
    webpage: WebpageMeta,
    description: ReaderHTMLContent,
    // 可选字段
    boardId?: string,
    playUrl?: string,
    publishedAt?: Date,
    heroImageUrl?: string,
    authors?: Author[],
    position?: BoardPositionInfo,
  ) {
    super(
      id,
      createdAt,
      updatedAt,
      creatorId,
      spaceId,
      parentId,
      title,
      extra,
      SnipType.VIDEO,
      SnipFrom.WEBPAGE,
      status,
      visibility,
      boardId,
      undefined,
      position,
    );

    this.webpage = webpage;
    this.description = description;
    this.playUrl = playUrl;
    this.publishedAt = publishedAt;
    this.heroImageUrl = heroImageUrl;
    this.authors = authors;
  }

  static async create(
    param: CreateVideoParam,
    boardPositionService?: BoardPositionDomainService,
  ): Promise<Video> {
    const snip = await Snip.create(
      {
        ...param,
        type: SnipType.VIDEO,
        from: SnipFrom.WEBPAGE,
        title: param.title || param.webpage.getTitle(),
      },
      boardPositionService,
    );

    const video = new Video(
      snip.id,
      snip.createdAt,
      snip.updatedAt,
      snip.creatorId,
      snip.spaceId,
      snip.parentId,
      snip.title,
      snip.extra,
      snip.status,
      snip.visibility,
      param.webpage,
      param.description,
      snip.boardId,
      param.playUrl,
      param.publishedAt,
      param.heroImageUrl,
      param.authors,
      snip.position,
    );

    video.isNew = true;
    return video;
  }

  /**
   * 更新视频信息
   */
  update(param: UpdateVideoParam): void {
    // 更新基础 Snip 字段
    super.update({
      title: param.title,
      status: param.status,
      visibility: param.visibility,
      extra: param.extra,
    });

    // 更新 Video 特有字段
    if (param.webpage !== undefined) {
      this.webpage = param.webpage;
    }

    if (param.description !== undefined) {
      this.description = param.description;
    }

    if (param.playUrl !== undefined) {
      this.playUrl = param.playUrl;
    }

    if (param.publishedAt !== undefined) {
      this.publishedAt = param.publishedAt;
    }

    if (param.heroImageUrl !== undefined) {
      this.heroImageUrl = param.heroImageUrl;
    }

    if (param.authors !== undefined) {
      this.authors = param.authors;
    }
  }

  /**
   * 检查是否可以提取 Hero 图片 URL
   */
  canExtractHeroImageUrl(): boolean {
    return !this.status && !!this.heroImageUrl;
  }

  /**
   * 提取 Hero 图片 URL
   */
  extractHeroImageUrl(): string | undefined {
    return this.heroImageUrl;
  }

  /**
   * 从视频描述中提取所有图片 URL
   * 包括 Hero 图片和作者头像
   */
  extractImageUrls(): string[] {
    let imageUrls: string[] = [];

    // 使用 ReaderHTMLContent 的方法提取图片 URL
    if (this.description instanceof ReaderHTMLContent) {
      imageUrls = this.description.extractImageUrls();
    }

    // 添加 Hero 图片
    if (this.heroImageUrl) {
      imageUrls.push(this.heroImageUrl);
    }

    // 添加作者头像
    if (this.authors) {
      this.authors.forEach((author) => {
        if (author.picture) {
          try {
            new URL(author.picture); // 验证 URL 格式
            imageUrls.push(author.picture);
          } catch {
            // 忽略无效的 URL
          }
        }
      });
    }

    // 去重并返回
    return Array.from(new Set(imageUrls));
  }

  /**
   * 替换图片 URL（用于图片转存后更新链接）
   */
  replaceImageUrls(
    imageMapping: { originalUrl: string; url: string; [key: string]: unknown }[],
  ): void {
    imageMapping.forEach((image) => {
      const originalUrl = image.originalUrl;
      const newUrl = image.url;

      // 替换描述中的图片 URL
      if (this.description instanceof ReaderHTMLContent) {
        this.description = this.description.replaceImageUrls([
          { originalUrl: originalUrl, url: newUrl },
        ]);
      }

      // 更新 Hero 图片 URL
      if (this.heroImageUrl === originalUrl) {
        this.heroImageUrl = newUrl;

        // 更新 Hero 图片元数据
        const heroImageMetadata: HeroImageMetadata = {};
        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash as string;
        }
        if (image.width) {
          heroImageMetadata.width = image.width as number;
        }
        if (image.height) {
          heroImageMetadata.height = image.height as number;
        }
        if (image.average) {
          heroImageMetadata.average = image.average as { r: number; g: number; b: number };
        }

        // 更新 extra 字段中的元数据
        const extraData = this.extra ? SafeParse(this.extra, false, {}) : {};
        (extraData as any).hero_image_metadata = heroImageMetadata;
        this.extra = JSON.stringify(extraData);
      }

      // 更新作者头像 URL
      if (this.authors) {
        this.authors.forEach((author) => {
          if (author.picture === originalUrl) {
            author.picture = newUrl;
          }
        });
      }
    });
  }

  /**
   * 获取视频的显示标题
   */
  getDisplayTitle(): string {
    return this.title || this.webpage.getTitle();
  }

  /**
   * 获取视频的显示描述
   */
  getDisplayDescription(): string {
    return this.webpage.getDescription() || this.description.getPlain()?.substring(0, 200) || '';
  }

  /**
   * 检查视频是否有有效的播放 URL
   */
  hasPlayUrl(): boolean {
    return !!this.playUrl;
  }

  /**
   * 获取视频的语言
   */
  getLanguage(): LanguageEnum | undefined {
    return this.description.getLanguage();
  }

  /**
   * 获取 Hero 图片元数据
   */
  getHeroImageMetadata(): HeroImageMetadata | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.hero_image_metadata;
  }

  /**
   * 获取视频时长（从 extra 字段中获取）
   */
  getDuration(): number | undefined {
    if (!this.extra) {
      return undefined;
    }

    const extraData = SafeParse(this.extra, false, {}) as any;
    return extraData.duration;
  }

  /**
   * 设置视频时长（保存到 extra 字段中）
   */
  setDuration(duration: number): void {
    const extraData = this.extra ? SafeParse(this.extra, false, {}) : {};
    (extraData as any).duration = duration;
    this.extra = JSON.stringify(extraData);
  }

  /**
   * 获取视频平台信息（从 webpage 中推断）
   */
  getVideoPlatform(): string | undefined {
    const host = this.webpage.getSite().host;
    if (host.includes('youtube.com') || host.includes('youtu.be')) {
      return 'YouTube';
    }
    if (host.includes('vimeo.com')) {
      return 'Vimeo';
    }
    if (host.includes('bilibili.com')) {
      return 'Bilibili';
    }
    return undefined;
  }

  /**
   * 检查是否是在线视频（需要网络播放）
   */
  isOnlineVideo(): boolean {
    return !!this.playUrl;
  }

  /**
   * 从 UnknownWebpage 创建 Video 实体
   * 模仿 youapp 的 Video.ofUnknownWebpage 方法
   */
  static ofUnknownWebpage(
    unknownWebpage: UnknownWebpage,
    param: {
      webpage: WebpageMeta;
      playUrl?: string;
      title: string;
      heroImageUrl?: string;
      description: ReaderHTMLContent;
      authors?: Author[];
      publishedAt?: Date;
      extra?: string;
      visibility?: VisibilityType;
    },
  ): Video {
    return new Video(
      unknownWebpage.id,
      unknownWebpage.createdAt,
      unknownWebpage.updatedAt,
      unknownWebpage.creatorId,
      unknownWebpage.spaceId,
      unknownWebpage.parentId,
      param.title,
      param.extra || unknownWebpage.extra,
      undefined, // 重置状态为 null
      param.visibility || unknownWebpage.visibility,
      param.webpage,
      param.description,
      unknownWebpage.boardId,
      param.playUrl,
      param.publishedAt,
      param.heroImageUrl,
      param.authors,
      unknownWebpage.position,
    );
  }
}
