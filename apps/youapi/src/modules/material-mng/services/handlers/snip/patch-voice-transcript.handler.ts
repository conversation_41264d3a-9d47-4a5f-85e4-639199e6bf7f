/**
 * Patch Voice Transcript Handler
 * 更新语音字幕处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/create_snip.ts (patchVoiceTranscript function)
 *
 * Architecture:
 * - Uses material-mng/snip.entity.ts as domain entity
 * - Creates transcript blocks via CreateSnipBlocksCommand
 */

import { Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { SubtitleHandler } from '@repo/common/content/subtitle';
import { ContentFormatEnum, LanguageEnum } from '@/common/types';
import { SafeParse } from '@/common/utils';
import { getLanguageEnum } from '@/common/utils/language';
import { InternalSubtitle, MinimaxSubtitle } from '../../../dto/snip/patch-voice-transcript.dto';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreateSnipBlocksCommand } from '../../commands/snip/create-snip-blocks.command';
import { PatchVoiceTranscriptCommand } from '../../commands/snip/patch-voice-transcript.command';

@CommandHandler(PatchVoiceTranscriptCommand)
export class PatchVoiceTranscriptHandler implements ICommandHandler<PatchVoiceTranscriptCommand> {
  private static readonly logger = new Logger(PatchVoiceTranscriptHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly commandBus: CommandBus,
  ) {}

  async execute(command: PatchVoiceTranscriptCommand): Promise<void> {
    const { userId, spaceId, snipId, subtitleFileUrl } = command;
    let transcript = command.transcript || '';

    // 1. Language detection and transcript processing
    let detectedLanguage = getLanguageEnum(transcript) || LanguageEnum['en-US'];

    // 2. Handle subtitle file URL if provided
    if (subtitleFileUrl) {
      try {
        const response = await fetch(subtitleFileUrl);
        const text = await response.text();
        const subtitles: MinimaxSubtitle[] = SafeParse(text, false, []);

        // Detect language from first subtitle
        if (subtitles.length > 0) {
          detectedLanguage = getLanguageEnum(subtitles[0].text) || LanguageEnum['en-US'];
        }

        // Transform external format to internal format
        const internalSubtitles: InternalSubtitle[] = subtitles.map((subtitle) => ({
          start: subtitle.time_begin,
          end: subtitle.time_end,
          speaker: '',
          content: [subtitle.text],
        }));

        transcript = JSON.stringify(internalSubtitles);
      } catch (error) {
        PatchVoiceTranscriptHandler.logger.error(
          `Failed to fetch/parse subtitle file from URL: ${subtitleFileUrl}`,
          error,
        );
        throw new Error('Failed to process subtitle file');
      }
    }

    // 3. Get snip and verify authorization
    const snip = await this.snipRepository.getById(snipId);
    // TODO: Add authorization check
    // await authzDomain.authzSnip(userId, snip);

    // 4. Process transcript with SubtitleHandler
    const handler = SubtitleHandler.fromRaw(transcript);
    const content = {
      raw: handler.toRawValue(),
      plain: handler.toPlainValue(),
      format: ContentFormatEnum.SUBTITLE,
      language: detectedLanguage,
    };

    // 5. Create transcript blocks using CreateSnipBlocksCommand
    const createBlocksCommand = new CreateSnipBlocksCommand(snipId, {
      transcriptContents: [content],
    });

    await this.commandBus.execute(createBlocksCommand);

    PatchVoiceTranscriptHandler.logger.log(
      `Successfully updated voice transcript for snip ${snipId}`,
    );
  }
}
