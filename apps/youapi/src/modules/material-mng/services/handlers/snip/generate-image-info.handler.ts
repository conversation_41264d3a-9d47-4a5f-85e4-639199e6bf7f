/**
 * Generate Image Info Handler - 生成图片信息处理器
 *
 * 使用 AI 分析图片并生成标题和描述信息
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/generate-image-info.ts
 * - /youapp/src/lib/domain/snip/index.ts (generateImageInfo)
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';
import { SafeParse } from '@/common/utils';
import { FileDomainService } from '@/domain/file';
import { UserDomainService } from '@/domain/user';
import { YouappLangfuseLLM } from '@/infra/youllm';
import { Image } from '../../../domain/snip/models/image.entity';
import { UntransferredFileMeta, UploadFileMeta } from '../../../domain/snip/models/type';
import { SnipRepository } from '../../../repositories/snip.repository';
import { GenerateImageInfoCommand } from '../../commands/snip/generate-image-info.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

// AI 响应格式定义
const ImageInfoSchema = z.object({
  title: z.string(),
  description: z.string(),
});

type ImageInfoResult = z.infer<typeof ImageInfoSchema>;

@CommandHandler(GenerateImageInfoCommand)
export class GenerateImageInfoHandler implements ICommandHandler<GenerateImageInfoCommand> {
  private readonly logger = new Logger(GenerateImageInfoHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly snipDtoService: SnipDtoService,
    private readonly fileDomainService: FileDomainService,
    private readonly userDomainService: UserDomainService,
    private readonly youllm: YouappLangfuseLLM,
  ) {}

  async execute(command: GenerateImageInfoCommand): Promise<any> {
    const { imageId, userId } = command;

    this.logger.debug(`Generating image info: imageId=${imageId}, userId=${userId}`);

    // 1. 获取并验证图片实体
    const snip = await this.snipRepository.getById(imageId);

    if (!(snip instanceof Image)) {
      this.logger.warn(`Snip ${imageId} is not an image type`);
      throw new BadRequestException('It is not an image.');
    }

    // 2. 使用 AI 生成图片信息
    await this.generateImageInfoForImage(snip, userId);

    // 3. 重新读取更新后的实体（解决并发修改问题）
    // FIXME 注释说明了一个已知问题：在同步搜索引擎的时候，image 的 description 居然被修改了，这里先暂时重新读取一个新的
    const updatedSnip = await this.snipRepository.getById(imageId);

    // 4. 返回 DTO
    return this.snipDtoService.toDtoList([updatedSnip])[0];
  }

  /**
   * 为图片生成信息
   */
  private async generateImageInfoForImage(image: Image, userId: string): Promise<void> {
    try {
      // 获取图片访问 URL
      const imageUrl = this.getImageUrl(image);

      // 调用 AI 生成图片信息
      const info = await this.generateImageInfoByUrl(imageUrl, userId);

      // 更新图片实体
      // 注意：description 在数据库中映射到 annotation_raw 字段
      image.title = info.title;
      image.description = info.description;

      // 保存更新
      await this.snipRepository.save(image);

      this.logger.debug(`Generated image info for ${image.id}: title="${info.title}"`);
    } catch (error) {
      this.logger.error(`Failed to generate image info for ${image.id}`, error);
      throw error;
    }
  }

  /**
   * 根据 URL 生成图片信息
   */
  private async generateImageInfoByUrl(imageUrl: string, userId: string): Promise<ImageInfoResult> {
    // 获取用户的 AI 响应语言偏好
    const aiLanguage = await this.userDomainService.getPrimaryResponseLanguage({
      user_id: userId,
    });

    this.logger.debug(`Calling AI service for image: ${imageUrl}, language: ${aiLanguage}`);

    // 调用 LLM 服务
    const response = await this.youllm.generateImageInfo(
      {
        aiLanguage,
        image_url: imageUrl,
      },
      false, // streamMode
      {
        useCache: false,
        traceArgs: {
          metadata: {
            imageUrl: imageUrl,
          },
        },
        modelOptions: {
          response_format: zodResponseFormat(ImageInfoSchema, 'generate-image-info-schema'),
        },
      },
    );

    // 解析响应
    const jsonStr = response.choices[0]?.message?.content;
    if (!jsonStr) {
      throw new Error('AI service returned empty response');
    }

    const result = SafeParse(jsonStr, false, {}) as ImageInfoResult;

    this.logger.debug(`AI generated image info: ${JSON.stringify(result)}`);

    return result;
  }

  /**
   * 获取图片的访问 URL
   */
  private getImageUrl(image: Image): string {
    const file = image.file;

    if (image.isUntransferredFile()) {
      // 未转存的外部图片
      return (file as UntransferredFileMeta).originalUrl;
    } else {
      // 已转存的内部图片
      return this.fileDomainService.getFileUrlByStorageUrl((file as UploadFileMeta).storageUrl);
    }
  }
}
