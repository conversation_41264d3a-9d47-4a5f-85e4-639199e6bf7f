/**
 * AI Provider Implementation using Vercel AI SDK
 * This replaces the traditional provider folder implementation
 */

import { createAmazonBedrock } from '@ai-sdk/amazon-bedrock';
import { createAnthropic } from '@ai-sdk/anthropic';
import { AzureOpenAIProvider, createAzure } from '@ai-sdk/azure';
import { createElevenLabs } from '@ai-sdk/elevenlabs';
import { createVertex } from '@ai-sdk/google-vertex';
import { createOpenAI, openai } from '@ai-sdk/openai';
import { EmbeddingModelV1, LanguageModelV1, ProviderV1 } from '@ai-sdk/provider';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { wrapLanguageModel } from 'ai';
import { LLMProviders, LLMs, MODEL_DEFINITION, ProviderCapabilityEnum } from '@/common/types';
import { RedisService } from '@/infra/redis';
import { createCacheMiddleware } from './middleware/cache-middleware';
import { scratchpadMiddleware } from './middleware/remove-middleware';
import { createMinimax } from './minimax';

// Azure OpenAI configuration constants (from existing azure.ts)
const AZURE_DEPLOYMENT_NAME_MAP = {
  [LLMs.GPT_4O]: 'gpt-4o',
  [LLMs.GPT_4O_MINI]: 'gpt-4o-mini',
  [LLMs.TEXT_EMBEDDING_3_LARGE]: 'text-embedding-3-large',
  [LLMs.GPT_41]: 'gpt-4.1',
  [LLMs.O4_MINI]: 'o4-mini',
};

const AZURE_MODEL_REGION_MAP = {
  [LLMs.GPT_4O]: 'west-us-3',
  [LLMs.GPT_4O_MINI]: 'west-us-3',
  [LLMs.TEXT_EMBEDDING_3_LARGE]: 'west-us-3',
  [LLMs.GPT_41]: 'east-us2',
  [LLMs.O4_MINI]: 'east-us2',
};

const AZURE_REGION_KEY_NAME_MAP = {
  'west-us-3': 'AZURE_API_KEY_WEST_US_3',
  'east-us2': 'AZURE_API_KEY_EAST_US_2',
};

// Vertex AI configuration constants (from existing vertexai.ts)
const VERTEX_MODEL_REGION_MAP = {
  [LLMs.GEMINI_25_PRO]: 'us-central1',
  [LLMs.GEMINI_25_FLASH]: 'us-central1',
  [LLMs.GEMINI_25_FLASH_LITE]: 'global',
  [LLMs.GEMINI_2_FLASH]: 'us-west1',
  [LLMs.GEMINI_2_FLASH_LITE]: 'us-west1',
};

// Provider configuration interface
interface ProviderConfig {
  provider: LLMProviders;
  createInstance: (configService: ConfigService) => any;
  modelMapping?: Record<string, string>; // Maps internal model names to provider-specific names
  supportedCapabilities: ProviderCapabilityEnum[];
}

// Registry of all available providers
const PROVIDER_CONFIGS: Record<LLMProviders, ProviderConfig> = {
  [LLMProviders.OPENAI]: {
    provider: LLMProviders.OPENAI,
    createInstance: (configService: ConfigService) =>
      createOpenAI({
        apiKey: configService.get<string>('OPENAI_API_KEY'),
      }),
    supportedCapabilities: [
      ProviderCapabilityEnum.TEXT,
      ProviderCapabilityEnum.EMBEDDING,
      ProviderCapabilityEnum.IMAGE,
      ProviderCapabilityEnum.SPEECH,
    ],
  },

  [LLMProviders.ANTHROPIC]: {
    provider: LLMProviders.ANTHROPIC,
    // TODO: add key rotation manager here
    createInstance: (configService: ConfigService) =>
      createAnthropic({
        apiKey: configService.get<string>('ANTHROPIC_API_KEY'),
      }),
    modelMapping: {
      [LLMs.CLAUDE_35_SONNET]: 'claude-3-5-sonnet-latest',
      [LLMs.CLAUDE_37_SONNET]: 'claude-3-7-sonnet-latest',
      [LLMs.CLAUDE_37_SONNET_THINKING]: 'claude-3-7-sonnet-latest',
      [LLMs.CLAUDE_4_SONNET]: 'claude-sonnet-4-20250514',
    },
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.AMAZON_BEDROCK]: {
    provider: LLMProviders.AMAZON_BEDROCK,
    createInstance: (configService: ConfigService) =>
      createAmazonBedrock({
        region: configService.get<string>('AWS_REGION', 'us-west-2'),
        accessKeyId: configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      }),
    modelMapping: {
      [LLMs.CLAUDE_35_SONNET]: 'us.anthropic.claude-3-5-sonnet-20241022-v2:0',
      [LLMs.CLAUDE_37_SONNET]: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      [LLMs.CLAUDE_37_SONNET_THINKING]: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      [LLMs.CLAUDE_4_SONNET]: 'us.anthropic.claude-sonnet-4-20250514-v1:0',
      [LLMs.DEEPSEEK_REASONER]: 'us.deepseek.r1-v1:0',
    },
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.VERTEXAI]: {
    provider: LLMProviders.VERTEXAI,
    createInstance: () => {
      // 根据 region 动态创建
      return null;
    },
    modelMapping: {
      [LLMs.GEMINI_25_PRO]: 'gemini-2.5-pro',
      [LLMs.GEMINI_25_FLASH]: 'gemini-2.5-flash',
      [LLMs.GEMINI_25_FLASH_LITE]: 'gemini-2.5-flash-lite-preview-06-17',
      [LLMs.GEMINI_2_FLASH]: 'gemini-2.0-flash',
      [LLMs.GEMINI_2_FLASH_LITE]: 'gemini-2.0-flash-lite',
    },
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.AZURE_OPENAI]: {
    provider: LLMProviders.AZURE_OPENAI,
    createInstance: () => {
      // Azure requires dynamic configuration based on model
      // This is a placeholder - actual instances will be created per-model
      return null;
    },
    modelMapping: {
      [LLMs.GPT_4O]: 'gpt-4o',
      [LLMs.GPT_4O_MINI]: 'gpt-4o-mini',
      [LLMs.TEXT_EMBEDDING_3_LARGE]: 'text-embedding-3-large',
      [LLMs.GPT_41]: 'gpt-4.1',
      [LLMs.O4_MINI]: 'o4-mini',
    },
    supportedCapabilities: [ProviderCapabilityEnum.TEXT, ProviderCapabilityEnum.EMBEDDING],
  },

  [LLMProviders.DEEPSEEK]: {
    provider: LLMProviders.DEEPSEEK,
    createInstance: (configService: ConfigService) =>
      createOpenAI({
        baseURL: 'https://api.deepseek.com/v1',
        apiKey: configService.get<string>('DEEPSEEK_API_KEY'),
      }),
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.ALIYUN]: {
    provider: LLMProviders.ALIYUN,
    createInstance: (configService: ConfigService) =>
      createOpenAI({
        baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
        apiKey: configService.get<string>('ALIYUN_API_KEY'),
      }),
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.AI_HUB_MIX]: {
    provider: LLMProviders.AI_HUB_MIX,
    createInstance: (configService: ConfigService) =>
      createOpenAI({
        baseURL: 'https://api.aihubmix.com/v1',
        apiKey: configService.get<string>('AIHUBMIX_API_KEY'),
      }),
    modelMapping: {
      [LLMs.CLAUDE_4_SONNET]: 'claude-sonnet-4-20250514',
      [LLMs.CLAUDE_37_SONNET]: 'claude-3-7-sonnet-20250219',
      [LLMs.CLAUDE_37_SONNET_THINKING]: 'claude-3-7-sonnet-20250219',
      [LLMs.CLAUDE_35_SONNET]: 'claude-3-5-sonnet-latest',
      [LLMs.CLAUDE_35_HAIKU]: 'claude-3-5-haiku-latest',
      [LLMs.DEEPSEEK_REASONER]: 'DeepSeek-R1',
      [LLMs.GEMINI_25_PRO]: 'gemini-2.5-pro-preview-03-25',
      [LLMs.GEMINI_25_FLASH]: 'gemini-2.5-flash-preview-05-20-nothink',
      [LLMs.GEMINI_25_FLASH_LITE]: 'gemini-2.5-flash-lite-preview-06-17',
      [LLMs.GEMINI_2_FLASH]: 'gemini-2.0-flash',
      [LLMs.GEMINI_2_FLASH_LITE]: 'gemini-2.0-flash-lite',
      [LLMs.O3_MINI]: 'o3-mini',
      [LLMs.QWEN_PLUS]: 'qwen-plus',
      [LLMs.QWEN_MAX]: 'qwen-max',
      [LLMs.QWEN_TURBO]: 'qwen-turbo',
    },
    supportedCapabilities: [ProviderCapabilityEnum.TEXT],
  },

  [LLMProviders.MINIMAX]: {
    provider: LLMProviders.MINIMAX,
    createInstance: (configService: ConfigService) =>
      createMinimax({
        apiKey: configService.get<string>('MINIMAX_API_KEY'),
        groupId: configService.get<string>('MINIMAX_GROUP_ID'),
      }),
    supportedCapabilities: [ProviderCapabilityEnum.SPEECH],
  },

  [LLMProviders.ELEVENLABS]: {
    provider: LLMProviders.ELEVENLABS,
    createInstance: (configService: ConfigService) =>
      createElevenLabs({
        apiKey: configService.get<string>('ELEVENLABS_API_KEY'),
      }),
    supportedCapabilities: [ProviderCapabilityEnum.SPEECH],
  },
};

/**
 * AI Provider Service - Central registry for all AI providers
 * NestJS Injectable Service using ConfigService for environment variables
 */
@Injectable()
export class ModelProviderService {
  private readonly providerInstances = new Map<string, ProviderV1>();

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Get the provider instance for a given provider type
   * For Azure OpenAI, we need to create instances per model due to region/deployment differences
   */
  getProviderInstance(providerType: LLMProviders, model?: LLMs): ProviderV1 {
    const cacheKey = model ? `${providerType}:${model}` : providerType;

    if (!this.providerInstances.has(cacheKey)) {
      const config = PROVIDER_CONFIGS[providerType];
      if (!config) {
        throw new Error(`Unsupported provider: ${providerType}`);
      }

      // Handle special cases that require per-model instances
      if (providerType === LLMProviders.AZURE_OPENAI && model) {
        const instance = this.createAzureOpenAIInstance(model);
        this.providerInstances.set(cacheKey, instance as unknown as ProviderV1);
      } else if (providerType === LLMProviders.VERTEXAI && model) {
        const instance = this.createVertexAIInstance(model);
        this.providerInstances.set(cacheKey, instance as unknown as ProviderV1);
      } else {
        this.providerInstances.set(cacheKey, config.createInstance(this.configService));
      }
    }
    return this.providerInstances.get(cacheKey);
  }

  /**
   * Create Azure OpenAI instance with proper region and deployment configuration
   */
  private createAzureOpenAIInstance(model: LLMs): AzureOpenAIProvider {
    const deployment = AZURE_DEPLOYMENT_NAME_MAP[model as keyof typeof AZURE_DEPLOYMENT_NAME_MAP];
    if (!deployment) {
      throw new Error(`Unsupported model for Azure OpenAI: ${model}`);
    }

    const region = AZURE_MODEL_REGION_MAP[model as keyof typeof AZURE_MODEL_REGION_MAP];
    if (!region) {
      throw new Error(`No available region for model ${model} in Azure OpenAI`);
    }

    const regionKeyName =
      AZURE_REGION_KEY_NAME_MAP[region as keyof typeof AZURE_REGION_KEY_NAME_MAP];
    const apiKey =
      this.configService.get<string>(regionKeyName) ??
      this.configService.get<string>('AZURE_API_KEY');

    if (!apiKey) {
      throw new Error(`No API key for Azure OpenAI in region ${region}`);
    }

    return createAzure({
      resourceName: `youmind-${region}`,
      apiKey,
    });
  }

  /**
   * Create Vertex AI instance with proper region configuration based on model
   */
  private createVertexAIInstance(model: LLMs) {
    const mappedModel = PROVIDER_CONFIGS[LLMProviders.VERTEXAI].modelMapping?.[model];
    if (!mappedModel) {
      throw new Error(`Unsupported model for Vertex AI: ${model}`);
    }

    const region = VERTEX_MODEL_REGION_MAP[model as keyof typeof VERTEX_MODEL_REGION_MAP];
    if (!region) {
      throw new Error(`No available region for model ${model} in Vertex AI`);
    }

    const projectId = this.configService.get<string>('GOOGLE_CLOUD_PROJECT');
    const clientEmail = this.configService.get<string>('GOOGLE_CLIENT_EMAIL');
    const privateKey = this.configService.get<string>('GOOGLE_PRIVATE_KEY');

    if (!projectId) {
      throw new Error('GOOGLE_CLOUD_PROJECT environment variable is not configured');
    }
    if (!clientEmail || !privateKey) {
      throw new Error(
        'GOOGLE_CLIENT_EMAIL and GOOGLE_PRIVATE_KEY environment variables are not configured',
      );
    }

    return createVertex({
      project: projectId,
      location: region,
      googleAuthOptions: {
        credentials: {
          client_email: clientEmail,
          private_key: privateKey.replace(/\\n/g, '\n'),
        },
      },
    });
  }

  /**
   * Get the appropriate provider for a given model
   */
  getProviderForModel(model: LLMs): LLMProviders {
    const modelDef = MODEL_DEFINITION.get(model);
    if (!modelDef || !modelDef.providers || modelDef.providers.length === 0) {
      throw new Error(`No providers found for model: ${model}`);
    }

    // Return the first available provider (primary provider)
    return modelDef.providers[0];
  }

  /**
   * Get the mapped model name for a provider (if mapping exists)
   */
  getMappedModelName(provider: LLMProviders, model: LLMs): string {
    const config = PROVIDER_CONFIGS[provider];
    if (config.modelMapping && config.modelMapping[model]) {
      return config.modelMapping[model];
    }
    return model; // Return original model name if no mapping
  }

  /**
   * Get a language model instance for chat/text generation
   */
  getLanguageModel(
    model: LLMs,
    options?: { useCache?: boolean; provider?: LLMProviders },
  ): LanguageModelV1 {
    const provider = options?.provider || this.getProviderForModel(model);
    const providerInstance = this.getProviderInstance(provider, model);
    const mappedModelName = this.getMappedModelName(provider, model);

    // Check if provider supports text generation capability
    const config = PROVIDER_CONFIGS[provider];
    if (!config.supportedCapabilities.includes(ProviderCapabilityEnum.TEXT)) {
      throw new Error(`Provider ${provider} does not support text generation capability`);
    }

    let baseModel: LanguageModelV1;
    // Handle different provider types
    switch (provider) {
      case LLMProviders.AZURE_OPENAI: {
        // For Azure, we need to use the deployment name from our mapping
        const deployment =
          AZURE_DEPLOYMENT_NAME_MAP[model as keyof typeof AZURE_DEPLOYMENT_NAME_MAP];
        baseModel = providerInstance.languageModel(deployment);
        break;
      }
      case LLMProviders.VERTEXAI: {
        // For Vertex AI, use the mapped model name
        baseModel = providerInstance.languageModel(mappedModelName);
        break;
      }
      default:
        baseModel = providerInstance.languageModel(mappedModelName);
    }

    const middlewares = [scratchpadMiddleware];
    if (options?.useCache) {
      const cacheMiddleware = createCacheMiddleware(this.redisService, provider, model);
      middlewares.push(cacheMiddleware);
    }

    return wrapLanguageModel({ model: baseModel, middleware: middlewares });
  }

  /**
   * Get embedding model with optional caching middleware
   * Currently only supports OpenAI embeddings
   */
  getEmbeddingModel(model: LLMs = LLMs.TEXT_EMBEDDING_3_LARGE): EmbeddingModelV1<string> {
    // Embeddings are primarily available through OpenAI
    const provider = LLMProviders.OPENAI;
    const baseModel = this.getEmbeddingModelByProvider(provider, model);

    return baseModel;
  }

  /**
   * Get embedding model by provider
   * Currently only supports OpenAI embeddings
   */
  private getEmbeddingModelByProvider(provider: LLMProviders, model: LLMs) {
    if (provider !== LLMProviders.OPENAI) {
      throw new Error(
        `Embeddings are currently only supported through OpenAI. Requested provider: ${provider}`,
      );
    }

    return openai.embedding(model) as unknown as EmbeddingModelV1<string>;
  }

  /**
   * Get a speech model instance
   */
  getSpeechModel(modelName: string = 'tts-1', preferredProvider?: LLMProviders) {
    const provider = preferredProvider || LLMProviders.OPENAI; // Default to OpenAI for speech
    const providerInstance = this.getProviderInstance(provider);

    // Check if provider supports speech capability
    const config = PROVIDER_CONFIGS[provider];
    if (!config.supportedCapabilities.includes(ProviderCapabilityEnum.SPEECH)) {
      throw new Error(`Provider ${provider} does not support speech generation capability`);
    }

    return providerInstance.speechModel(modelName);
  }

  /**
   * Check if a provider supports a specific capability
   */
  supportsCapability(provider: LLMProviders, capability: ProviderCapabilityEnum): boolean {
    const config = PROVIDER_CONFIGS[provider];
    return config?.supportedCapabilities.includes(capability) || false;
  }

  /**
   * Get all available providers for a specific capability
   */
  getProvidersForCapability(capability: ProviderCapabilityEnum): LLMProviders[] {
    return Object.values(LLMProviders).filter((provider) =>
      this.supportsCapability(provider, capability),
    );
  }

  /**
   * Clear cached provider instances (useful for testing or config changes)
   */
  clearCache(): void {
    this.providerInstances.clear();
  }
}
