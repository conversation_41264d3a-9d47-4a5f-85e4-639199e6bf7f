import { Injectable } from '@nestjs/common';
import { YLTTSOptions } from '@/infra/youllm';
import { SpeechRunner } from '../runners/speech';

export interface GenerateSpeechResult {
  hash: string;
  audio_url: string;
  subtitleFile: string;
  extraInfo: any;
}

export interface GenerateSpeechOptions {
  prompt: string;
  voice?: YLTTSOptions['voice'];
  emotion?: YLTTSOptions['emotion'];
  model?: YLTTSOptions['model'];
  subtitleEnable?: YLTTSOptions['subtitleEnable'];
}

@Injectable()
export class SpeechService {
  constructor() {}

  /**
   * Generate a speech - calls the youapp tool function equivalent
   */
  async generateSpeech(
    userId: string,
    options: GenerateSpeechOptions,
  ): Promise<GenerateSpeechResult> {
    const runner = new SpeechRunner();

    const params: Partial<YLTTSOptions> = {
      voice: options.voice,
      emotion: options.emotion,
      model: options.model,
      subtitleEnable: options.subtitleEnable,
    };

    return await runner.generateSpeech(options.prompt, params, userId);
  }
}
