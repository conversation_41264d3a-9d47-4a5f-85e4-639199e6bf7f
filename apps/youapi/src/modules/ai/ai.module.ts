import { Modu<PERSON> } from '@nestjs/common';
import { CommonModule } from '@/common/common.module';
import { InfraModule } from '@/infra/infra.module';
import { AIController } from './controllers/ai.controller';
import { PromptService } from './prompt/index.service';
import { ModelProviderService } from './providers/index.service';
import { ExplainService } from './services/explain.service';
import { ImageService } from './services/image.service';
import { OverviewService } from './services/overview.service';
import { SelfDescService } from './services/self-desc.service';
import { SpeechService } from './services/speech.service';
import { TingwuService } from './services/tingwu.service';

@Module({
  imports: [CommonModule, InfraModule],
  controllers: [AIController],
  providers: [
    ExplainService,
    ImageService,
    SpeechService,
    OverviewService,
    ModelProviderService,
    PromptService,
    TingwuService,
    SelfDescService,
  ],
  exports: [
    ExplainService,
    ImageService,
    SpeechService,
    OverviewService,
    ModelProviderService,
    PromptService,
    TingwuService,
    SelfDescService,
  ],
})
export class AiModule {}
