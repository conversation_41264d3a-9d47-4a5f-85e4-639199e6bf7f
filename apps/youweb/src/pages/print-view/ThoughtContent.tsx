'use client';

import { Thought } from '@repo/common/types/thought/types';
import { ThoughtPreview } from '@repo/ui-business-editor';

interface ThoughtContentProps {
  thought: Thought;
  isPrint?: boolean;
}

export function ThoughtContent({ thought, isPrint = false }: ThoughtContentProps) {
  if (isPrint) {
    // 打印模式，使用简化的样式
    return (
      <div className="print-avoid-break">
        <ThoughtPreview thought={thought} />
      </div>
    );
  }

  // 常规模式
  return <ThoughtPreview thought={thought} />;
}
