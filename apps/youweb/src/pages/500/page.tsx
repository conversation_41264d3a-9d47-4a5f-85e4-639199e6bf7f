import { InternalError } from '@repo/ui/components/custom/internal-error';
import * as Sentry from '@sentry/react';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export default function InternalErrorPage() {
  const location = useLocation();
  const { error, from } = (location?.state || {}) as { error: Error; from: 'client' | 'server' };

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <InternalError
      from={from}
      error={error}
      onGoHomeClick={() => {
        // [FIXME] 源介看这里
      }}
    />
  );
}
