import { PlanList } from '@repo/ui/components/custom/plan-list';
import { YouMindLogo, YouMindTextLogo } from '@/components/ui/custom/logo';

export const UpgradePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-card flex flex-col relative overflow-hidden">
      <div
        className="p-6 relative z-10 flex items-center cursor-pointer"
        onClick={() => {
          window.location.href = '/';
        }}
      >
        <YouMindLogo size={16} />
        <YouMindTextLogo className="ml-[6px]" size={16} />
      </div>

      <div className="headline1 text-center mb-6">Upgrade your plan</div>

      <PlanList />
    </div>
  );
};
