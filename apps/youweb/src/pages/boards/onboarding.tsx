import type { BoardWithCountVO } from '@repo/common/types/board/types';
import { BoardStatusEnum, BoardTemplateEnum, BoardTypeEnum } from '@repo/common/types/board/types';
import { Button } from '@repo/ui/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@repo/ui/components/ui/dialog';
import { toast } from '@repo/ui/components/ui/sonner';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import BoardCard from '@/components/board/board-card';
import BoardCreator from '@/components/board/board-creator';
import { StartCard } from '@/components/start-card';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';

const DEFAULT_BOARDS = [
  {
    name: 'Nobel Prize',
    snips_count: 6,
    thoughts_count: 0,
    id: '-1',
    description: 'it should not be shown',
    status: BoardStatusEnum.IN_PROGRESS,
    type: BoardTypeEnum.NORMAL,
    icon: {
      name: '<PERSON><PERSON><PERSON>',
      color: '--function-orange',
    },
    created_at: new Date(),
    updated_at: new Date(),
    hero_image_urls: ['https://cdn.gooo.ai/assets/nobel-prize-board-1.png'],
    template: BoardTemplateEnum.TEMPLATE_2,
  },
  {
    name: 'Yellowstone National Park',
    snips_count: 8,
    thoughts_count: 0,
    id: '-1',
    description: 'it should not be shown',
    status: BoardStatusEnum.IN_PROGRESS,
    type: BoardTypeEnum.NORMAL,
    icon: {
      name: 'Mountain',
      color: '--function-brown',
    },
    created_at: new Date(),
    updated_at: new Date(),
    hero_image_urls: ['https://cdn.gooo.ai/assets/yellowstone-board-3.png'],
    template: BoardTemplateEnum.TEMPLATE_1,
  },
  {
    name: 'What is an LLM?',
    snips_count: 5,
    thoughts_count: 0,
    id: '-1',
    description: 'it should not be shown',
    status: BoardStatusEnum.IN_PROGRESS,
    type: BoardTypeEnum.NORMAL,
    icon: {
      name: 'Book',
      color: '--foreground',
    },
    created_at: new Date(),
    updated_at: new Date(),
    hero_image_urls: ['https://cdn.gooo.ai/assets/llm-board-1.png'],
    template: BoardTemplateEnum.TEMPLATE_3,
  },
];

export function BoardOnboarding() {
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const handleBoardCreated = (value: Partial<BoardWithCountVO>) => {
    navigate(`/boards/${value.id}`);
  };

  return (
    <div className="flex flex-col">
      <div className="mb-6 text-base font-normal text-muted-foreground">
        {
          'Save hours on drafting your article using board, and stay in flow with your research materials at your fingertips.'
        }
      </div>

      <div className="flex gap-5 w-full">
        <StartCard
          title={'Create your first Board'}
          headerImageUrl={'https://cdn.gooo.ai/assets/board-start-card-1.png'}
          bottom={
            <BoardCreator onBoardChanged={handleBoardCreated} syncToBoardList>
              <DialogTrigger asChild>
                <Button className="h-8 w-[72px] rounded-full text-sm">{'Create'}</Button>
              </DialogTrigger>
            </BoardCreator>
          }
        />

        {/* <StartCard
          title={"Board streamlines \n your journey from idea to draft"}
          description={"Learn Board fast in 3 simple steps."}
          headerImageUrl={
            "https://cdn.gooo.ai/assets/board-learn-start-card.png"
          }
          bottom={
            <Button
              variant="ghost"
              className="px-0 h-8 text-sm font-normal rounded-full text-muted-foreground hover:bg-transparent"
              onClick={() => {
                setCornerTourPopupState({
                  isOpen: true,
                  type: "board-tour",
                });
              }}
            >
              <Lightbulb size={16} className="mr-1" />
              {"Learn more"}
            </Button>
          }
        /> */}
      </div>

      <div className="mt-10 mb-5 text-sm font-medium text-caption">
        {'Or quickly try with one of the templates below.'}
      </div>

      <div className="flex gap-5">
        {DEFAULT_BOARDS.map((board, idx) => (
          <Dialog key={idx}>
            <DialogTrigger asChild>
              <BoardCard
                key={idx}
                className="mt-5 h-[180px] w-[256px] cursor-pointer"
                board={board}
              />
            </DialogTrigger>
            <DialogContent className="h-[424px] bg-white">
              <div className="flex flex-col justify-between items-center mt-4 text-foreground">
                <div className="text-xl font-semibold">{board.name}</div>
                <div className="mt-2 text-sm text-center text-muted-foreground">
                  This template contains some snips, and once added, they will be automatically
                  saved to your YouMind.
                </div>
                <img
                  src={board.hero_image_urls?.[0]}
                  alt="board placeholder"
                  className="-mt-10 max-w-[360px]"
                />
                <Button
                  size="sm"
                  className={cn(
                    'mt-8 h-8 font-normal rounded-full hover:bg-text-foreground leading-0 w-[120px]',
                    {
                      'cursor-not-allowed bg-disabled': isLoading,
                    },
                  )}
                  onClick={async () => {
                    setIsLoading(true);
                    try {
                      const { data: newBoard, error } = await callHTTP(
                        '/api/v1/createBoardFromTemplate',
                        {
                          method: 'POST',
                          body: {
                            template: board.template,
                          },
                        },
                      );
                      if (error) {
                        return;
                      }
                      toast.success('Board created successfully');
                      navigate(`/boards/${newBoard.id}`);
                    } catch (error) {
                      console.error(error);
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                >
                  {isLoading ? <Loader2 size={16} className="mr-1 animate-spin" /> : ''}
                  {'Create'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </div>
  );
}
