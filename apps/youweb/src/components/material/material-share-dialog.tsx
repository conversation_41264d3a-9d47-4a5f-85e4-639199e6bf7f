import type { EntityTypeEnum } from '@repo/common/types/entity/enum';
import type { SnipVO } from '@repo/common/types/snip/app-types';
import type { ThoughtVO } from '@repo/common/types/thought/types';
import { VisibilityEnum } from '@repo/common/types/visibility/enum';
import { Button } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Input } from '@repo/ui/components/ui/input';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { useRequest } from 'ahooks';
import { Check, ExternalLink, Globe, Share2, X } from 'lucide-react';
import qs from 'query-string';
import { useCallback, useEffect, useRef, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { LinkIcon } from '@/components/icon/link';
import { SimpleLoading } from '@/components/simple-loading';
import { useTranslation } from '@/hooks/useTranslation';
import { callHTTP } from '@/utils/callHTTP';
import { copy } from '@/utils/clipboard';
import { cn } from '@/utils/utils';

export function MaterialShareDialog(props: {
  material: SnipVO | ThoughtVO;
  type: EntityTypeEnum;
  onConfirm?: () => void;
}) {
  const { material, type, onConfirm } = props;

  const { t } = useTranslation('Library.Board');
  const [isPublic, setIsPublic] = useState(material?.visibility === VisibilityEnum.PUBLIC);
  const [open, setOpen] = useState(false);
  const [shortLink, setShortLink] = useState('');
  const [isCopied, setIsCopied] = useState(false);
  const [isCopyButtonChecked, setIsCopyButtonChecked] = useState(false);
  const copyButtonTimerRef = useRef<NodeJS.Timeout>();

  const { trackButtonClick } = useTrackActions();

  // debounce 逻辑：处理复制按钮图标状态恢复
  const debounceCopyButtonReset = useCallback(() => {
    if (copyButtonTimerRef.current) {
      clearTimeout(copyButtonTimerRef.current);
    }
    copyButtonTimerRef.current = setTimeout(() => {
      setIsCopyButtonChecked(false);
    }, 2000);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (copyButtonTimerRef.current) {
        clearTimeout(copyButtonTimerRef.current);
      }
    };
  }, []);

  const response = useRequest(
    async () => {
      const query = qs.stringify({ entityType: type, entityId: material.id });
      const { data } = await callHTTP(`/api/v1/getShortLink?${query}`, {
        method: 'GET',
      });
      if (data?.active && data?.short_id) {
        setShortLink(data.short_id);
        setIsPublic(true);
      } else {
        setIsPublic(false);
      }
    },
    {
      ready: !!open,
      refreshDeps: [open],
    },
  );
  const loading = response.loading;

  useEffect(() => {
    setIsPublic(material?.visibility === VisibilityEnum.PUBLIC);
  }, [material?.visibility]);

  const shareLink = `${process.env.NEXT_PUBLIC_YOUMIND_SITE_ORIGIN}/${shortLink}`;

  const handlePublishStatusChanged = async (checked: boolean) => {
    // 上报埋点
    trackButtonClick('share_publish_switch_click', {
      entity_type: type,
      entity_id: material.id,
      target_state: checked,
    });

    const { data, error } = await callHTTP(
      checked ? '/api/v1/material/publish' : '/api/v1/material/unpublish',
      {
        method: 'POST',
        body: {
          id: material.id,
          type,
        },
      },
    );

    if (error) {
      return;
    }

    if (checked) {
      setShortLink(data.short_id);
      copy(`${process.env.NEXT_PUBLIC_YOUMIND_SITE_ORIGIN}/${data.short_id}`);
      setIsCopied(true);
    } else {
      setOpen(false);
    }
    setIsPublic(checked);

    onConfirm?.();
  };

  const handleShareClick = async () => {
    trackButtonClick('share_click', {
      entity_type: type,
      entity_id: material?.id,
      is_public: isPublic,
    });

    // 如果未分享，立即发送分享请求
    if (!isPublic) {
      await handlePublishStatusChanged(true);
    }
    setOpen(true);
  };

  return (
    <DropdownMenu
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);
        }
      }}
    >
      <DropdownMenuTrigger asChild onClick={handleShareClick}>
        <Button
          async
          variant="outline"
          size="sm"
          className={cn('mr-1 h-8 rounded-full px-3 text-accent-foreground')}
        >
          {!isPublic ? (
            <>
              <Share2 size={16} className="mr-2" />
              {t('share')}
            </>
          ) : (
            <>
              <Globe size={16} className="mr-2" />
              {t('sharing')}
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[408px] rounded-xl p-5" align="end" sideOffset={8}>
        <h3 className="flex items-center justify-between text-base font-semibold text-foreground">
          <div className="flex items-center">
            <Globe size={16} className="mr-[6px]" />
            Share to the web
          </div>
          <Button variant="ghost" size="icon" className="!h-4 !w-4" onClick={() => setOpen(false)}>
            <X size={16} />
          </Button>
        </h3>
        <div className="mt-4">
          {loading ? (
            <SimpleLoading />
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-caption">
                Share the content with a public link. Anyone with the link can access.
              </p>
              <div className="relative aspect-[752/416] w-full overflow-hidden rounded-lg">
                <img
                  src="https://cdn.gooo.ai/assets/share-image-new.png"
                  alt="Share Preview"
                  className="object-cover w-full"
                />
              </div>
              <div className="flex">
                <div className="relative -mr-px grid grow grid-cols-1 focus-within:relative">
                  <ExternalLink
                    size={14}
                    className="absolute left-[10px] top-1/2 -translate-y-1/2 cursor-pointer text-foreground"
                    onClick={() => {
                      window.open(shareLink, '_blank');
                    }}
                  />
                  <Input
                    className="col-start-1 row-start-1 block !h-8 w-full rounded-l-md rounded-r-none border-r-0 pl-[30px] pr-3 text-muted-foreground"
                    readOnly
                    autoFocus={false}
                    value={shareLink}
                  />
                </div>
                <CopyToClipboard
                  text={shareLink}
                  onCopy={() => {
                    setIsCopied(true);
                    setIsCopyButtonChecked(true);
                    debounceCopyButtonReset();
                    trackButtonClick('share_copy_link_click', {
                      entity_type: type,
                      entity_id: material.id,
                      share_link: shareLink,
                    });
                  }}
                >
                  <Button
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    size="sm"
                    className="z-10 flex !h-8 shrink-0 items-center gap-x-1.5 rounded-l-none rounded-r-md !border-l border-l-input !px-2 hover:bg-gray-50"
                  >
                    {isCopyButtonChecked ? (
                      <Check size={16} className="mr-[2px]" />
                    ) : (
                      <LinkIcon size={16} className="mr-[2px]" />
                    )}
                    Copy
                  </Button>
                </CopyToClipboard>
              </div>
              <div className="mt-6 flex items-center justify-between">
                {isCopied ? (
                  <div className="flex items-center text-sm text-blue-600">
                    <Check size={16} className="mr-2" />
                    Link copied. Paste to share.
                  </div>
                ) : (
                  <div />
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="!h-8 rounded-full"
                  async
                  onClick={async () => {
                    await handlePublishStatusChanged(false);
                  }}
                >
                  Stop sharing
                </Button>
              </div>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
