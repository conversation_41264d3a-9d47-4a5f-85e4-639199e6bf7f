import { TitleTypeEnum } from '@repo/common/types/thought/types';
import {
  Blockquote,
  Bold,
  BulletList,
  base64ToUint8Array,
  CodeBlock,
  DIFF_CHANGE_TYPE,
  DiffTransformUtils,
  Divider,
  encodeDocToBase64,
  Heading,
  Image,
  Italic,
  Link,
  ListItem,
  type MarkdownSerializer,
  markdownSerializer,
  OrderedList,
  Paragraph,
  Strike,
  TaskItem,
  TaskList,
  Text,
  Underline,
} from '@repo/editor-common';
import { uploadImages } from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/core';
import { methodRegistry } from '@youmindinc/jsbridge';
import { debounce } from 'lodash-es';
import { yUndoPluginKey } from 'y-prosemirror';
import { applyUpdate, type Doc, transact, type UndoManager } from 'yjs';
import { RegisterToNativeMethodName } from './const';
import { NativeMethodController } from './native-method-controller';
import {
  type EditorPatchDataType,
  type InjectNativeMethodEnableOptions,
  type InsertHeadingNodeOptions,
  type InsertImageNodeOptions,
  type InsertNodeOptions,
  type MarkInfo,
  type NodeInfo,
  type OnSelectionChangeOptions,
  type OpenLinkOptions,
  type RedoOptions,
  type RegisterToNativeMethodEnableOptions,
  SelectionType,
  type SetLinkOptions,
  type SetNodeMarkOptions,
  type ShowToastOptions,
  type UndoOptions,
} from './type';
import { getWorkflowFromEditor } from './utils';

interface NativeMessageControllerOptions {
  editor: Editor;
  ydoc: Doc;
  registerToNativeMethodEnableOptions?: RegisterToNativeMethodEnableOptions;
  injectToNativeMethodEnableOptions?: InjectNativeMethodEnableOptions;
}

export class NativeMessageController {
  private readonly editor: Editor;

  private readonly ydoc: Doc;

  private nativeMethodController: NativeMethodController;

  private diffTransformUtils = new DiffTransformUtils();

  private registerToNativeMethodEnableOptions: RegisterToNativeMethodEnableOptions | undefined;

  private injectToNativeMethodEnableOptions: InjectNativeMethodEnableOptions | undefined;

  constructor(options: NativeMessageControllerOptions) {
    this.editor = options.editor;
    this.ydoc = options.ydoc;

    this.nativeMethodController = new NativeMethodController({
      editor: this.editor,
      ydoc: this.ydoc,
      registerToNativeMethodEnableOptions: options.registerToNativeMethodEnableOptions,
      injectToNativeMethodEnableOptions: options.injectToNativeMethodEnableOptions,
    });

    this.registerToNativeMethodEnableOptions = options.registerToNativeMethodEnableOptions;

    this.injectToNativeMethodEnableOptions = options.injectToNativeMethodEnableOptions;

    this.exportMethod();

    this.nativeMethodController.initMethod();
    this.initSelectionListener();
    this.initUndoRedoStackMonitor();
  }

  private initSelectionListener() {
    this.editor.on('selectionUpdate', () => {
      this.handleSelectionUpdate();
    });
  }

  private handleSelectionUpdate = debounce(() => {
    const { state } = this.editor;
    const { selection } = state;
    const { empty, from, to } = selection;

    const selectionType = empty ? SelectionType.CURSOR : SelectionType.SELECTION;

    const markInfoList: MarkInfo[] = [];
    const nodeInfoList: NodeInfo[] = [];

    if (this.editor.isActive(Bold.name)) {
      markInfoList.push({ type: Bold.name });
    }
    if (this.editor.isActive(Italic.name)) {
      markInfoList.push({ type: Italic.name });
    }
    if (this.editor.isActive(Underline.name)) {
      markInfoList.push({ type: Underline.name });
    }
    if (this.editor.isActive(Strike.name)) {
      markInfoList.push({ type: Strike.name });
    }
    if (this.editor.isActive(Link.name)) {
      const attrs = this.editor.getAttributes(Link.name);
      markInfoList.push({
        type: Link.name,
        attrs,
      } as SetLinkOptions);
    }

    const { doc } = state;
    doc.nodesBetween(from, to, (node) => {
      if (node.type.name !== Text.name) {
        const attrs = node.attrs || {};
        nodeInfoList.push({
          type: node.type.name,
          attrs,
        });
      }
      return true;
    });

    const selectionChangeOptions: OnSelectionChangeOptions = {
      selectionType,
      selectionInfo: {
        markInfoList,
        nodeInfoList,
      },
    };

    this.onSelectionChange(selectionChangeOptions);
  }, 10);

  exportMethod() {
    try {
      if (
        this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.GET_EDITOR_PATCH_DATA]
      ) {
        methodRegistry.exportMethod(RegisterToNativeMethodName.GET_EDITOR_PATCH_DATA, async () => {
          return this.getEditorPatchData();
        });
      }

      if (
        this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.SET_EDITOR_PATCH_DATA]
      ) {
        methodRegistry.exportMethod(
          RegisterToNativeMethodName.SET_EDITOR_PATCH_DATA,
          async (data: EditorPatchDataType) => {
            return this.setEditorPatchData(data);
          },
        );
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.INSERT_NODE]) {
        methodRegistry.exportMethod(
          RegisterToNativeMethodName.INSERT_NODE,
          async (options: InsertNodeOptions) => {
            return this.insertNode(options);
          },
        );
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.FOCUS_EDITOR]) {
        methodRegistry.exportMethod(RegisterToNativeMethodName.FOCUS_EDITOR, async () => {
          return this.focusEditor();
        });
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.SET_NODE_MARK]) {
        methodRegistry.exportMethod(
          RegisterToNativeMethodName.SET_NODE_MARK,
          async (options: SetNodeMarkOptions) => {
            return this.setNodeMark(options);
          },
        );
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.INVOKE_REDO]) {
        methodRegistry.exportMethod(RegisterToNativeMethodName.INVOKE_REDO, async () => {
          return this.invokeRedo();
        });
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.INVOKE_UNDO]) {
        methodRegistry.exportMethod(RegisterToNativeMethodName.INVOKE_UNDO, async () => {
          return this.invokeUndo();
        });
      }

      if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.BLUR_EDITOR]) {
        methodRegistry.exportMethod(RegisterToNativeMethodName.BLUR_EDITOR, async () => {
          return this.editorBlur();
        });
      }
    } catch (error) {
      console.error('exportMethod error', error);
    }

    if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.COPY_SELECTION]) {
      methodRegistry.exportMethod(RegisterToNativeMethodName.COPY_SELECTION, async () => {
        return this.copySelection();
      });
    }

    if (this.registerToNativeMethodEnableOptions?.[RegisterToNativeMethodName.CUT_SELECTION]) {
      methodRegistry.exportMethod(RegisterToNativeMethodName.CUT_SELECTION, async () => {
        return this.cutSelection();
      });
    }
  }

  async setEditorPatchData(data: EditorPatchDataType) {
    try {
      console.log('setEditorPatchData', data);
      const { title, content } = data;
      this.workflow?.editTitle(title);
      const { raw } = content;
      if (!raw) {
        this.editor.commands.setContent('');
        return {
          success: true,
        };
      }
      const contentUnit8Array = base64ToUint8Array(raw);
      transact(
        this.ydoc,
        () => {
          applyUpdate(this.ydoc, contentUnit8Array);
        },
        'deco',
        false,
      );
      return {
        success: true,
      };
    } catch (error) {
      console.error(error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'unknown error',
      };
    }
  }

  getEditorPatchData(): EditorPatchDataType {
    const docWithoutDiffInfo = this.diffTransformUtils.extractContent(
      this.editor.state.doc,
      DIFF_CHANGE_TYPE.ADDED,
    );
    const contentMarkdown = markdownSerializer.serialize(docWithoutDiffInfo);
    const data = {
      id: this.workflow?.getId() || '',
      title: this.workflow?.title || '',
      content: {
        plain: contentMarkdown,
        raw: encodeDocToBase64(this.ydoc),
      },
      titleType: this.workflow?.titleType || TitleTypeEnum.DEFAULT,
    };
    console.log('getEditorPatchData', data);
    return data;
  }

  private async insertNode(options: InsertNodeOptions) {
    console.log('insertNode', options);
    const { type } = options;

    const isEmpty = this.isParagraphEmpty();

    switch (type) {
      case Blockquote.name:
        if (isEmpty) {
          this.editor.chain().focus().setBlockquote().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: 'blockquote',
              content: [
                {
                  type: 'paragraph',
                },
              ],
            })
            .scrollCursorIntoView()
            .run();
        }
        break;

      case Divider.name:
        this.editor.chain().focus().setHorizontalRule().scrollCursorIntoView().run();
        break;

      case Heading.name: {
        const requestedLevel = (options as InsertHeadingNodeOptions).attrs.level;
        if (this.editor.isActive(Heading.name, { level: requestedLevel })) {
          this.editor.chain().focus().setParagraph().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .setHeading({
              level: requestedLevel,
            })
            .scrollCursorIntoView()
            .run();
        }
        break;
      }

      case BulletList.name:
        if (isEmpty) {
          this.editor.chain().focus().toggleBulletList().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: BulletList.name,
              content: [
                {
                  type: ListItem.name,
                  content: [
                    {
                      type: Paragraph.name,
                    },
                  ],
                },
              ],
            })
            .scrollCursorIntoView()
            .run();
        }
        break;

      case OrderedList.name:
        if (isEmpty) {
          this.editor.chain().focus().toggleOrderedList().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: OrderedList.name,
              content: [
                {
                  type: ListItem.name,
                  content: [
                    {
                      type: Paragraph.name,
                    },
                  ],
                },
              ],
            })
            .scrollCursorIntoView()
            .run();
        }
        break;

      case TaskList.name:
        if (isEmpty) {
          this.editor.chain().focus().toggleTaskList().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: TaskList.name,
              content: [
                {
                  type: TaskItem.name,
                  attrs: { checked: false },
                  content: [
                    {
                      type: 'paragraph',
                    },
                  ],
                },
              ],
            })
            .scrollCursorIntoView()
            .run();
        }
        break;

      case CodeBlock.name:
        if (isEmpty) {
          this.editor.chain().focus().setCodeBlock().scrollCursorIntoView().run();
        } else {
          this.editor
            .chain()
            .focus()
            .insertContent({
              type: 'codeBlock',
              content: [
                {
                  type: 'text',
                  text: '',
                },
              ],
            })
            .scrollCursorIntoView()
            .run();
        }
        break;

      case Image.name:
        await this.insertImageList(options as InsertImageNodeOptions);
        break;

      default:
        console.error(`unknown node type: ${type}`);
    }
    this.handleSelectionUpdate();
  }

  // Helper method to check if current paragraph is empty
  private isParagraphEmpty(): boolean {
    const { selection, doc } = this.editor.state;
    const { from } = selection;

    const $pos = this.editor.state.doc.resolve(from);
    const currentNode = $pos.node();

    if (currentNode.type.name === Paragraph.name) {
      return currentNode.content.size === 0;
    }

    return false;
  }

  private async insertImageList(options: InsertImageNodeOptions) {
    const {
      attrs: { url },
    } = options as InsertImageNodeOptions;
    const imageUrlList = Array.isArray(url) ? url : [url];
    const view = this.editor.view;

    try {
      const imageFiles = await Promise.all(
        imageUrlList.map(async (imageUrl) => {
          const response = await fetch(imageUrl);
          const blob = await response.blob();
          const fileName = imageUrl.split('/').pop() || 'image.jpg';
          return new File([blob], fileName, { type: blob.type });
        }),
      );

      uploadImages(this.editor, imageFiles, view.state.selection.from);
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  }

  private async setNodeMark(options: SetNodeMarkOptions) {
    const { type } = options;
    switch (type) {
      case Bold.name:
        if (this.editor.isActive(Bold.name)) {
          this.editor.chain().focus().unsetBold().run();
        } else {
          this.editor.chain().focus().setBold().run();
        }
        break;

      case Italic.name:
        if (this.editor.isActive(Italic.name)) {
          this.editor.chain().focus().unsetItalic().run();
        } else {
          this.editor.chain().focus().setItalic().run();
        }
        break;

      case Underline.name:
        if (this.editor.isActive(Underline.name)) {
          this.editor.chain().focus().unsetUnderline().run();
        } else {
          this.editor.chain().focus().setUnderline().run();
        }
        break;

      case Strike.name:
        if (this.editor.isActive(Strike.name)) {
          this.editor.chain().focus().unsetStrike().run();
        } else {
          this.editor.chain().focus().setStrike().run();
        }
        break;

      case Link.name:
        if (this.editor.isActive(Link.name)) {
          this.editor.chain().focus().unsetLink().run();
        } else {
          this.editor
            .chain()
            .focus()
            .setLink({ href: (options as SetLinkOptions).attrs.url })
            .run();
        }
        break;

      default:
        console.error(`unknown mark type: ${type}`);
    }

    // Manually trigger selection update after setting mark
    this.handleSelectionUpdate();
  }

  private invokeRedo() {
    console.log('invokeRedo');
    if (!this.undoManager) {
      return {
        success: false,
        error: 'UndoManager not available',
      };
    }

    try {
      this.undoManager.redo();
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Redo failed',
      };
    }
  }

  private invokeUndo() {
    console.log('invokeUndo');
    if (!this.undoManager) {
      return {
        success: false,
        error: 'UndoManager not available',
      };
    }

    try {
      this.undoManager.undo();
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Undo failed',
      };
    }
  }

  async editorIsReady() {
    return await this.nativeMethodController.editorIsReady();
  }

  async updateEditorData(data: EditorPatchDataType) {
    return await this.nativeMethodController.updateEditorData(data);
  }

  async setUndoOptions(options: UndoOptions) {
    return await this.nativeMethodController.setUndoOptions(options);
  }

  async setRedoOptions(options: RedoOptions) {
    return await this.nativeMethodController.setRedoOptions(options);
  }

  async showToast(options: ShowToastOptions) {
    return await this.nativeMethodController.showToast(options);
  }

  async onSelectionChange(options: OnSelectionChangeOptions) {
    return await this.nativeMethodController.onSelectionChange(options);
  }

  get undoManager(): UndoManager | undefined {
    return yUndoPluginKey.getState(this.editor.view.state)?.undoManager;
  }

  get workflow() {
    return getWorkflowFromEditor(this.editor);
  }

  private initUndoRedoStackMonitor() {
    this.updateUndoRedoOptions();

    this.editor.on('transaction', () => {
      this.updateUndoRedoOptions();
    });
  }

  private updateUndoRedoOptions = debounce(() => {
    if (!this.undoManager) return;

    const undoEnabled = this.undoManager.undoStack.length > 0;
    const redoEnabled = this.undoManager.redoStack.length > 0;

    this.setUndoOptions({ enabled: undoEnabled });
    this.setRedoOptions({ enabled: redoEnabled });
  }, 200);

  onBlur() {
    this.nativeMethodController.onBlur();
  }

  onFocus() {
    this.nativeMethodController.onFocus();
  }

  openLink(options: OpenLinkOptions) {
    this.nativeMethodController.openLink(options);
  }

  focusEditor() {
    this.editor.commands.focus();
  }

  editorBlur() {
    this.editor.commands.blur();
  }

  copySelection() {
    // this.editor.commands.copySelection();
  }

  cutSelection() {
    // this.editor.commands.cutSelection();
  }
}

export type INativeMessageController = NativeMessageController;
