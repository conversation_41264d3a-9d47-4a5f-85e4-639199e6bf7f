import { DOC_FRAGMENT_FIELED } from '@repo/editor-common';
import type { EditorExtensionOptions } from '@repo/ui-business-editor';
import {
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
} from '@repo/ui-business-editor';
import { Collaboration } from '@tiptap/extension-collaboration';
import type { Doc } from 'yjs';
import { NativeMessageExtension } from '../extensions/native-message';
import type { NativeMessageExtensionOptions } from '../extensions/native-message/extension';

export interface MobileEditorExtensionOptions {
  ydoc: Doc;
  extensionsOptions?: EditorExtensionOptions & {
    nativeMessageExtensionOptions?: Omit<NativeMessageExtensionOptions, 'ydoc'>;
  };
}

export const getMobileEditorExtension = (options: MobileEditorExtensionOptions) => {
  const { ydoc, extensionsOptions } = options;
  return [
    Collaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    NativeMessageExtension.configure({
      ydoc,
      ...extensionsOptions?.nativeMessageExtensionOptions,
    }),
    ...getCommonFunctionExtensionList(extensionsOptions),
    ...getMarkExtensionList(extensionsOptions),
    ...getNodeExtensionList(extensionsOptions),
  ];
};
