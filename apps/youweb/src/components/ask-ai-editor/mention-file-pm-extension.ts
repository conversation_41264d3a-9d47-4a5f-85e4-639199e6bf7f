import type { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import {
  Suggestion,
  type SuggestionKeyDownProps,
  type SuggestionProps,
} from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/core';
import { PluginKey } from '@tiptap/pm/state';
import { ReactRenderer } from '@tiptap/react';
import tippy, { type Instance } from 'tippy.js';
import type { MentionFileItem } from '@/hooks/ask-ai/useChatMention';
import {
  MentionFilePanel,
  type MentionFilePanelProps,
  type MentionFilePanelRef,
} from './mention-file-panel';

export const PM_EXTENSION_NAME = 'pm-mention-file';

export const EXTENSION_NAME = 'mention-file';

export const MentionFilePluginKey = new PluginKey(PM_EXTENSION_NAME);

export interface MentionFileInternalOptions {
  mentionTypeList: BoardItemTypeEnum[];
  mentionOptions: MentionFileItem[];
  recommendMentionOptions: MentionFileItem[];
  showSearch: boolean;
  HTMLAttributes: Record<string, unknown>;
  triggerChar: string;
}

export type MentionFileOptions = MentionFileInternalOptions & {
  HTMLAttributes: Record<string, unknown>;
  deleteTriggerWithBackspace: boolean;
  onAddMention?: (mention: MentionFileItem) => void;
  onRemoveMention?: (mention: MentionFileItem) => void;
  name?: string;
};

export function getMentionFilePlugin(editor: Editor, options: MentionFileOptions) {
  return Suggestion({
    editor,
    char: '@',
    allowSpaces: false,
    pluginKey: MentionFilePluginKey,
    startOfLine: false,
    command: ({ editor, range, props }) => {
      const nodeAfter = editor.view.state.selection.$to.nodeAfter;
      const overrideSpace = nodeAfter?.text?.startsWith(' ');

      if (overrideSpace) {
        range.to += 1;
      }

      editor
        .chain()
        .focus()
        .insertContentAt(range, [
          {
            type: EXTENSION_NAME,
            attrs: {
              id: props.id,
              label: props.label,
              mentionType: props.mentionType,
              // 有可能外部传入的 logo 是 jsx
              // logo: props.logo,
            },
          },
          {
            type: 'text',
            text: ' ',
          },
        ])
        .run();

      if (options.onAddMention) {
        const mentionItem: MentionFileItem = {
          id: props.id,
          display: props.label,
          type: props.mentionType,
          logo: props.logo,
        };
        options.onAddMention(mentionItem);
      }

      window.setTimeout(() => {
        editor.view.dom.ownerDocument.defaultView?.getSelection()?.collapseToEnd();
      }, 0);
    },
    render: () => {
      let component: ReactRenderer<MentionFilePanelRef, MentionFilePanelProps> | null = null;
      let popup: Instance | null = null;

      return {
        onStart: (props: SuggestionProps) => {
          // 检查编辑器是否聚焦，如果没有聚焦则不显示面板
          if (!editor.isFocused) {
            return;
          }

          const state = MentionFilePluginKey.getState(editor.state);
          if (!state) {
            return;
          }
          const mentionTypeList = state.mentionTypeList;
          const mentionOptions = state.mentionOptions;
          const recommendMentionOptions = state.recommendMentionOptions;
          const showSearch = state.showSearch;
          component = new ReactRenderer<MentionFilePanelRef, MentionFilePanelProps>(
            MentionFilePanel,
            {
              editor,
              props: {
                query: props.query,
                mentionTypeList,
                mentionOptions,
                recommendMentionOptions,
                showSearch,
                rememberLastHover: true,
                className: 'p-2',
                onSelect: (item: MentionFileItem) => {
                  props.command({
                    id: item.id,
                    label: item.display,
                    mentionType: item.type,
                    logo: item.logo,
                  });
                  popup?.hide();
                },
              },
            },
          );
          if (!props.clientRect || !props.decorationNode) {
            return;
          }
          component.element.classList.add('shadow-lg', 'rounded-xl');

          // @ts-expect-error tippy
          popup = tippy(props.decorationNode, {
            getReferenceClientRect: props.clientRect!,
            appendTo: () => editor.view.dom.parentElement!,
            content: component?.element,
            showOnCreate: true,
            interactive: true,
            trigger: 'manual',
            placement: 'top-start',
            onHidden: () => {
              const state = MentionFilePluginKey.getState(editor.state);
              if (state) {
                state.active = false;
              }
            },
          });
        },
        onUpdate(props: SuggestionProps) {
          // 检查编辑器是否聚焦，如果没有聚焦则隐藏面板
          if (!editor.isFocused) {
            popup?.hide();
            return;
          }

          const state = MentionFilePluginKey.getState(editor.state);
          if (!state) {
            return;
          }
          const mentionTypeList = state.mentionTypeList;
          const mentionOptions = state.mentionOptions;
          const recommendMentionOptions = state.recommendMentionOptions;
          const showSearch = state.showSearch;

          component?.updateProps({
            ...props,
            mentionTypeList,
            mentionOptions,
            recommendMentionOptions,
            showSearch,
            onSelect: (item: MentionFileItem) => {
              props.command({
                id: item.id,
                label: item.display,
                mentionType: item.type,
                logo: item.logo,
              });
              popup?.hide();
            },
          });

          if (!props.clientRect) {
            return;
          }
        },
        onKeyDown(props: SuggestionKeyDownProps) {
          if (props.event.key === 'Escape') {
            popup?.destroy();
            return true;
          }

          if (component?.element.isConnected) {
            const { event } = props;
            // Handle all navigation keys (up, down, left, right)
            if (
              event.key === 'ArrowUp' ||
              event.key === 'ArrowDown' ||
              event.key === 'ArrowLeft' ||
              event.key === 'ArrowRight'
            ) {
              const result = component.ref?.onKeyDown(props) ?? false;
              if (result) {
                event.preventDefault();
              }
              return result;
            }

            // Handle selection keys (Enter, Tab)
            if (event.key === 'Enter' || event.key === 'Tab') {
              const result = component.ref?.onKeyDown(props) ?? false;
              if (result) {
                event.preventDefault();
              }
              return result;
            }
          }
          return false;
        },
        onExit() {
          popup?.destroy();
          component?.destroy();
        },
      };
    },
  });
}
