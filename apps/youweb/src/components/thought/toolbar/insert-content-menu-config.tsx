import { Paragraph, uploadImages } from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/react';
import { Minus, Table } from 'lucide-react';
import type { ReactNode } from 'react';
import { ThoughtImageIcon } from '../icon/thought-image-icon';
import { ThoughtMermaidIcon } from '../icon/thought-mermaid-icon';

interface InserrContentMenuItem {
  icon: ReactNode;
  onClick: (editor: Editor) => void;
  label: string;
  markdownTip?: string;
}

export const InsertContentMenuConfig: InserrContentMenuItem[] = [
  {
    icon: <ThoughtImageIcon size={16} />,
    label: 'Image',
    onClick: (editor) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = true;
      input.onchange = async () => {
        const files = Array.from(input.files || []);
        if (files.length === 0) return;

        const view = editor.view;
        uploadImages(editor, files, view.state.selection.from);
      };
      input.click();
    },
  },
  {
    icon: <Table size={16} />,
    label: 'Table',
    onClick: (editor) => {
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: false })
        .scrollCursorIntoView()
        .run();
    },
  },
  {
    icon: <Minus size={16} />,
    label: 'Divider',
    markdownTip: '---',
    onClick: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setHorizontalRule()
        .scrollCursorIntoView()
        .run();
    },
  },
  {
    icon: <ThoughtMermaidIcon size={16} />,
    label: 'Text Diagram',
    onClick: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setMermaid()
        .scrollCursorIntoView()
        .run();
    },
  },
];
