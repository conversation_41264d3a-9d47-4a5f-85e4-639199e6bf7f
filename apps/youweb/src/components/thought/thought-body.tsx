import type { ThoughtEditorReadyParams } from '@repo/ui-business-editor';
import { isMobile, ThoughtNavigation, ThoughtNavigationMobile } from '@repo/ui-business-editor';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { setWorkflow } from '../editor-kit/extensions/thought-adpater-extension';
import { ThoughtBodyComponent } from './thought-body-component';
import { ThoughtToolbarComponent } from './toolbar/thought-toolbar-component';
import type { AIOptions, ThoughtBodyComponentRef, ThoughtBodyProps } from './type';
import { ThoughtWorkflow, type ThoughtWorkflowOptions } from './workflow';

const ThoughtBodyInner = (props: ThoughtBodyProps) => {
  const { id, onReady, aiOptions, user } = props;
  const aiOptionsRef = useRef<AIOptions | null>(null);
  const [editorReadyParams, setEditorReadyParams] = useState<ThoughtEditorReadyParams>();

  const thoughtBodyRef = useRef<ThoughtBodyComponentRef>(null);

  useEffect(() => {
    if (aiOptions) {
      aiOptionsRef.current = aiOptions;
    }
  }, [aiOptions]);

  const workflow = useMemo(() => {
    if (!editorReadyParams || !thoughtBodyRef.current) {
      return;
    }
    const workflowOptions: ThoughtWorkflowOptions = {
      ...editorReadyParams,
      id,
      indexedDB: null,
      thoughtComponentRef: thoughtBodyRef,
      aiOptionsRef,
      user,
    };
    const workflow = new ThoughtWorkflow(workflowOptions);
    setWorkflow(editorReadyParams.editor, workflow);
    return workflow;
  }, [editorReadyParams, thoughtBodyRef]);

  const renderNavigation = () => {
    if (!editorReadyParams) return;
    const { editor } = editorReadyParams;
    if (!editor) {
      return;
    }
    if (isMobile()) {
      return <ThoughtNavigationMobile editor={editor} />;
    }
    return <ThoughtNavigation editor={editor} />;
  };

  const onEditorReay = useCallback(
    (params: ThoughtEditorReadyParams) => {
      (window as any).youmindEditor = params.editor;
      onReady?.(params);
      setEditorReadyParams(params);
    },
    [onReady],
  );

  return (
    <>
      <ThoughtBodyComponent
        {...props}
        workflow={workflow}
        onReady={onEditorReay}
        ref={thoughtBodyRef}
      />
      {editorReadyParams?.editor && <ThoughtToolbarComponent editor={editorReadyParams.editor} />}
      {renderNavigation()}
    </>
  );
};

export const ThoughtBody = memo(ThoughtBodyInner, (prevProps, nextProps) => {
  // 只有当 id 相同时才认为是相同的组件，避免不必要的重新渲染
  return prevProps.id === nextProps.id;
});

ThoughtBody.displayName = 'ThoughtBody';
