import { type ThoughtVersion, ThoughtVersionTypeEnum } from '@repo/common/types/thought/types';
import { UserWithPreferenceVO } from '@repo/common/types/user/types';
import { base64ToJSON } from '@repo/editor-common';
import { getDiffBlockManage } from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/core';
import type { RefObject } from 'react';
import type { Doc } from 'yjs';
import { callHTTP } from '@/utils/callHTTP';
import type { ThoughtBodyComponentRef } from '../type';
import { ThoughtContentUtils } from './thought-content-utils';

interface ThoughtVersionUtilsOptions {
  id: string;
  editor: Editor;
  ydoc: Doc;
  componentRef: RefObject<ThoughtBodyComponentRef>;
}

interface AutoVersionSaveData {
  timestamp: number;
  plain: string;
}

const AUTO_VERSION_SAVE_KEY_PREFIX = 'youmind-auto-version-save-data-';

const AUTO_VERSION_INTERVAL_MS = 5 * 60 * 1000;

export class ThoughtVersionUtils {
  private id: string;
  private editor: Editor;
  private ydoc: Doc;
  private componentRef: RefObject<ThoughtBodyComponentRef>;

  constructor(options: ThoughtVersionUtilsOptions) {
    this.id = options.id;
    this.editor = options.editor;
    this.ydoc = options.ydoc;
    this.componentRef = options.componentRef;
  }

  async trySaveAutoVersion(contentMD: string, user?: UserWithPreferenceVO) {
    if (this.shouldCreateAutoVersion(contentMD)) {
      this.updateAutoVersionSaveData(contentMD);
      await this.createAutoVersion(contentMD, user);
    }
  }

  private async createAutoVersion(contentMD: string, user?: UserWithPreferenceVO) {
    const content = {
      raw: ThoughtContentUtils.getRawData(this.ydoc),
      plain: contentMD,
    };
    const sendParams = {
      thought_id: this.id,
      type: ThoughtVersionTypeEnum.AUTO,
      title: `Modified by ${user?.name || 'Unknown User'}`,
      content,
      thought_title: this.componentRef?.current?.getTitle(),
    };
    const { error } = await callHTTP('/api/v1/thought/createThoughtVersion', {
      method: 'POST',
      body: sendParams,
      silent: true,
    });
    if (error) {
      return false;
    }
    return true;
  }

  private updateAutoVersionSaveData(plain: string) {
    const data: AutoVersionSaveData = {
      timestamp: Date.now(),
      plain,
    };
    localStorage.setItem(this.getAutoVersionSaveKey(), JSON.stringify(data));
  }

  private shouldCreateAutoVersion(currentPlain: string): boolean {
    const lastSaveData = this.getLastAutoVersionSaveData();

    // 如果是第一次（没有保存数据），不自动保存，只更新数据
    if (lastSaveData === null) {
      this.updateAutoVersionSaveData(currentPlain);
      return false;
    }

    const now = Date.now();
    const timeDiff = now - lastSaveData.timestamp;

    // 如果时间未超过 5 分钟，不保存
    if (timeDiff < AUTO_VERSION_INTERVAL_MS) {
      return false;
    }

    // 如果内容没有变化，只更新时间戳，不保存版本
    if (currentPlain === lastSaveData.plain) {
      this.updateAutoVersionSaveData(currentPlain);
      return false;
    }

    // 时间超过 5 分钟且内容有变化，需要保存版本
    return true;
  }

  private getAutoVersionSaveKey() {
    return `${AUTO_VERSION_SAVE_KEY_PREFIX}${this.id}`;
  }

  private getLastAutoVersionSaveData(): AutoVersionSaveData | null {
    const saved = localStorage.getItem(this.getAutoVersionSaveKey());
    if (!saved) return null;

    try {
      const parsed = JSON.parse(saved);
      // 兼容旧数据格式（只有时间戳的情况）
      if (typeof parsed === 'number') {
        return { timestamp: parsed, plain: '' };
      }
      return parsed;
    } catch {
      return null;
    }
  }

  async getVersionList(): Promise<ThoughtVersion[]> {
    const { data } = await callHTTP<ThoughtVersion[]>('/api/v1/thought/listThoughtVersions', {
      method: 'POST',
      body: {
        thought_id: this.id,
      },
      silent: true,
    });
    if (!data || !Array.isArray(data)) {
      return [];
    }
    return data;
  }

  async deleteVersion(versionId: string) {
    const params = {
      id: versionId,
    };
    const { error } = await callHTTP('/api/v1/thought/deleteThoughtVersion', {
      method: 'POST',
      body: params,
      silent: true,
    });
    if (error) {
      return false;
    }
    return true;
  }

  async createManualVersion(title: string, description?: string) {
    const content = {
      raw: ThoughtContentUtils.getRawData(this.ydoc),
      plain: ThoughtContentUtils.getMarkdownData(this.editor.state.doc),
    };
    const sendParams = {
      thought_id: this.id,
      type: ThoughtVersionTypeEnum.MANUAL,
      title,
      description,
      content,
      thought_title: this.componentRef?.current?.getTitle(),
    };
    const { data, error } = await callHTTP<ThoughtVersion>('/api/v1/thought/createThoughtVersion', {
      method: 'POST',
      body: sendParams,
      silent: true,
    });
    if (error || !data) {
      return null;
    }
    return data;
  }

  async restoreThought(thoughtVersion: ThoughtVersion, user?: UserWithPreferenceVO) {
    try {
      const { thought_title, content } = thoughtVersion;
      const { raw } = content;
      const json = base64ToJSON(raw);
      this.editor.commands.setContent(json);
      this.componentRef?.current?.setTitle(thought_title);

      setTimeout(() => {
        getDiffBlockManage(this.editor)?.reRenderDiffInfo();
        this.editor.emit('update', {
          editor: this.editor,
          transaction: this.editor.state.tr,
        });
        this.createManualVersion(`Restore by ${user?.name || 'Unknown User'}`);
      }, 1000);
      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  }
}
