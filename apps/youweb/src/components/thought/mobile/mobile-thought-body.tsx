import type { Thought } from '@repo/common/types/thought/types';
import type { Editor } from '@tiptap/core';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { Doc } from 'yjs';
import { SimpleLoading } from '@/components/simple-loading';
import { callHTTP } from '@/utils/callHTTP';
import type { EditorOnReadyParams } from '../../editor-kit/thought-mobile-editor/mobile-editor';
import type { ThoughtBodyComponentRef } from '../type';
import { MobileThoughtWorkflow } from '../workflow';
import { getMobileThoughtBodyExtensionOptions } from './mobile-extension-options';
import { MobileThoughtBodyComponent } from './mobile-thought-body-component';

export interface MobileThoughtBodyProps {
  id?: string;
  thought?: Thought;
  onCreate?: (params: EditorOnReadyParams) => void;
}

export const MobileThoughtBody = (props: MobileThoughtBodyProps) => {
  const componentRef = useRef<ThoughtBodyComponentRef>(null);
  const [editor, setEditor] = useState<Editor | null>(null);
  const [ydoc, setYdoc] = useState<Doc | null>(null);
  const [thought, setThought] = useState<Thought | null>(props.thought ?? null);
  const [loadingThought, setLoadingThought] = useState(props.thought ?? false);
  const { id } = props;

  const workflow = useMemo(() => {
    if (!editor || !id || !ydoc || !componentRef.current) {
      return null;
    }
    const workflow = new MobileThoughtWorkflow({
      id,
      editor,
      ydoc,
      componentRef,
    });
    editor.commands.setWorkflow(workflow);
    return workflow;
  }, [editor, id, ydoc, componentRef]);

  useEffect(() => {
    return () => {
      workflow?.destroy();
    };
  }, [workflow]);

  useEffect(() => {
    // 如果外部传入了 thought 则不进行 API 请求再获取 thought
    if (props.thought) {
      setLoadingThought(false);
      return;
    }
    if (!id) {
      return;
    }
    setLoadingThought(true);

    const params: { id: string } = {
      id,
    };

    const fetchThought = async () => {
      const { data, error } = await callHTTP<Thought>('/api/v1/getThought', {
        method: 'POST',
        body: params,
        silent: true,
      });
      if (error) {
        return;
      }
      setLoadingThought(false);
      setThought(data);
    };

    fetchThought();
  }, [id, props.thought]);

  if (loadingThought || !id) {
    return (
      <div className="flex h-[100vh] w-full items-center justify-center pb-20">
        <SimpleLoading />
      </div>
    );
  }

  return (
    <MobileThoughtBodyComponent
      ref={componentRef}
      workflow={workflow}
      id={id}
      thought={thought}
      mobileEditorProps={{
        id,
        content: thought?.content.raw,
        storeOptions: {
          indexeddbStoreEnable: false,
        },
        onCreate: (params) => {
          setEditor(params.editor);
          setYdoc(params.ydoc);
          props.onCreate?.(params);
        },
        extensionsOptions: getMobileThoughtBodyExtensionOptions(),
      }}
    />
  );
};
