import { type ContentHandler, LanguageEnum } from '@youmindinc/youcommon';
// TEMPORARY: Commented out franc-min for Cloudflare Workers compatibility
// import { franc } from 'franc-min';
import { iso6393To1 } from 'iso-639-3';

/**
 * 全部支持的语言
 * follow ChatGPT
 */
export const GenerateLanguageKeysEnum = ['en-US', 'zh-CN'] as const;

enum ExtendedSystemEnum {
  system = 'system',
}
enum ExtendedContentEnum {
  'follow-content' = 'follow-content',
}

// 界面语言
export const DisplayLanguageEnum = {
  'en-US': LanguageEnum['en-US'],
} as const;
export type DisplayLanguageEnumKeys = keyof typeof DisplayLanguageEnum;

// AI 语言
export const AILanguageEnum = {
  ...ExtendedContentEnum,
  'en-US': LanguageEnum['en-US'],
  ...ExtendedSystemEnum,
  ...(Object.fromEntries(Object.entries(LanguageEnum).filter(([key]) => key !== 'en-US')) as Omit<
    typeof LanguageEnum,
    'en-US'
  >),
} as const;
export type AILanguageEnumKeys = keyof typeof AILanguageEnum;

// 通用
export type AllLanguageEnum = typeof AILanguageEnum & typeof DisplayLanguageEnum;

export type AllLanguageEnumKeys = keyof AllLanguageEnum;

export const LanguageNameMap: Record<AllLanguageEnumKeys, string> = {
  'follow-content': 'Auto',
  system: 'System',
  am: 'አማርኛ',
  ar: 'العربية',
  'bg-BG': 'български',
  'bn-BD': 'বাংলা',
  'bs-BA': 'bosanski',
  'ca-ES': 'catal\xe0',
  'cs-CZ': 'čeština',
  'da-DK': 'dansk',
  'de-DE': 'Deutsch',
  'el-GR': 'Ελληνικά',
  'en-US': 'English',
  'es-419': 'espa\xf1ol (Latinoam\xe9rica)',
  'es-ES': 'espa\xf1ol (Espa\xf1a)',
  'et-EE': 'eesti',
  'fi-FI': 'suomi',
  'fr-CA': 'fran\xe7ais (Canada)',
  'fr-FR': 'fran\xe7ais (France)',
  'gu-IN': 'ગુજરાતી',
  'hi-IN': 'हिन्दी',
  'hr-HR': 'hrvatski',
  'hu-HU': 'magyar',
  'hy-AM': 'հայերեն',
  'id-ID': 'Indonesia',
  'is-IS': '\xedslenska',
  'it-IT': 'italiano',
  'ja-JP': '日本語',
  'ka-GE': 'ქართული',
  kk: 'қазақ тілі',
  'kn-IN': 'ಕನ್ನಡ',
  'ko-KR': '한국어',
  lt: 'lietuvių',
  'lv-LV': 'latviešu',
  'mk-MK': 'македонски',
  ml: 'മലയാളം',
  mn: 'монгол',
  'mr-IN': 'मराठी',
  'ms-MY': 'Bahasa Melayu',
  'my-MM': 'မြန်မာ',
  'nb-NO': 'norsk bokm\xe5l',
  'nl-NL': 'Nederlands',
  pa: 'ਪੰਜਾਬੀ',
  'pl-PL': 'polski',
  'pt-BR': 'portugu\xeas (Brasil)',
  'pt-PT': 'portugu\xeas (Portugal)',
  'ro-RO': 'rom\xe2nă',
  'ru-RU': 'русский',
  'sk-SK': 'slovenčina',
  'sl-SI': 'slovenščina',
  'so-SO': 'Soomaali',
  'sq-AL': 'shqip',
  'sr-RS': 'српски',
  'sv-SE': 'svenska',
  'sw-TZ': 'Kiswahili',
  'ta-IN': 'தமிழ்',
  'te-IN': 'తెలుగు',
  'th-TH': 'ไทย',
  tl: 'Tagalog',
  'tr-TR': 'T\xfcrk\xe7e',
  'uk-UA': 'українська',
  ur: 'اردو',
  'vi-VN': 'Tiếng Việt',
  'zh-CN': '简体中文',
  'zh-HK': '繁體中文（香港）',
  'zh-TW': '繁體中文（台灣）',
} as const;

export type NormalLanguageType = {
  label: string;
  value: LanguageEnum;
  aliases?: string[];
};

export const NormalLanguageList: NormalLanguageType[] = [
  // 1. 英语 - 全球最通用
  {
    label: LanguageNameMap[LanguageEnum['en-US']],
    value: LanguageEnum['en-US'],
    aliases: [
      '英语',
      '英文',
      'English',
      '英',
      '美式英语',
      '美语',
      'American English',
      'US English',
      'en',
      'eng',
    ],
  },
  // 2. 中文 - 使用人数最多
  {
    label: LanguageNameMap[LanguageEnum['zh-CN']],
    value: LanguageEnum['zh-CN'],
    aliases: [
      '中文',
      '中国话',
      '汉语',
      'Chinese',
      'Mandarin',
      '中',
      '普通话',
      '国语',
      '简体中文',
      '简中',
      '大陆中文',
      'zh',
      'cn',
      'chn',
    ],
  },
  // 3. 西班牙语 - 第二大使用人数
  {
    label: LanguageNameMap[LanguageEnum['es-ES']],
    value: LanguageEnum['es-ES'],
    aliases: [
      '西班牙语',
      '西语',
      'Spanish',
      'Español',
      '西',
      '伊比利亚西语',
      '欧洲西语',
      'Castilian',
      'es',
      'spa',
      '西班牙文',
    ],
  },
  {
    label: LanguageNameMap[LanguageEnum['es-419']],
    value: LanguageEnum['es-419'],
    aliases: [
      '拉丁美洲西班牙语',
      '拉美西语',
      'Latin American Spanish',
      '墨西哥语',
      '阿根廷语',
      '拉美语',
      '南美西语',
    ],
  },
  // 4. 阿拉伯语 - 广泛使用
  {
    label: LanguageNameMap[LanguageEnum.ar],
    value: LanguageEnum.ar,
    aliases: ['阿拉伯语', '阿语', 'Arabic', '阿', '阿拉伯文', 'العربية', 'ar', 'ara'],
  },
  // 5. 印地语 - 印度主要语言
  {
    label: LanguageNameMap[LanguageEnum['hi-IN']],
    value: LanguageEnum['hi-IN'],
    aliases: ['印地语', 'Hindi', '印', '印度语', 'हिन्दी', 'hi', 'hin'],
  },
  // 6. 葡萄牙语
  {
    label: LanguageNameMap[LanguageEnum['pt-BR']],
    value: LanguageEnum['pt-BR'],
    aliases: [
      '巴西葡萄牙语',
      '葡语',
      'Portuguese',
      'Brazilian Portuguese',
      '葡',
      '巴西语',
      'pt',
      'por',
      '葡萄牙文',
    ],
  },
  {
    label: LanguageNameMap[LanguageEnum['pt-PT']],
    value: LanguageEnum['pt-PT'],
    aliases: [
      '葡萄牙语',
      '欧洲葡萄牙语',
      'Portuguese',
      'European Portuguese',
      '葡',
      '葡萄牙文',
      '欧葡',
    ],
  },
  // 7. 俄语
  {
    label: LanguageNameMap[LanguageEnum['ru-RU']],
    value: LanguageEnum['ru-RU'],
    aliases: ['俄语', '俄文', 'Russian', '俄', 'русский', 'ru', 'rus', '苏联语'],
  },
  // 8. 日语
  {
    label: LanguageNameMap[LanguageEnum['ja-JP']],
    value: LanguageEnum['ja-JP'],
    aliases: ['日语', '日文', 'Japanese', 'にほんご', '日', '日本语', 'ja', 'jpn', '和语'],
  },
  // 9. 德语
  {
    label: LanguageNameMap[LanguageEnum['de-DE']],
    value: LanguageEnum['de-DE'],
    aliases: ['德语', '德文', 'German', 'Deutsch', '德', '日耳曼语', 'de', 'deu', 'ger'],
  },
  // 10. 法语
  {
    label: LanguageNameMap[LanguageEnum['fr-FR']],
    value: LanguageEnum['fr-FR'],
    aliases: ['法语', '法文', 'French', 'Français', '法', '法兰西语', 'fr', 'fra', 'fre'],
  },
  {
    label: LanguageNameMap[LanguageEnum['fr-CA']],
    value: LanguageEnum['fr-CA'],
    aliases: ['加拿大法语', 'Canadian French', 'Québécois', '魁北克法语', '加法'],
  },
  // 11. 韩语
  {
    label: LanguageNameMap[LanguageEnum['ko-KR']],
    value: LanguageEnum['ko-KR'],
    aliases: ['韩语', '韩文', '朝鲜语', 'Korean', '한국어', '韩', '朝鲜文', 'ko', 'kor', '高丽语'],
  },
  // 12. 意大利语
  {
    label: LanguageNameMap[LanguageEnum['it-IT']],
    value: LanguageEnum['it-IT'],
    aliases: ['意大利语', '意语', 'Italian', 'Italiano', '意', '意大利文', 'it', 'ita'],
  },
  // 13. 土耳其语
  {
    label: LanguageNameMap[LanguageEnum['tr-TR']],
    value: LanguageEnum['tr-TR'],
    aliases: ['土耳其语', 'Turkish', 'Türkçe', '土', '土耳其文', 'tr', 'tur'],
  },
  // 14. 越南语
  {
    label: LanguageNameMap[LanguageEnum['vi-VN']],
    value: LanguageEnum['vi-VN'],
    aliases: ['越南语', 'Vietnamese', 'Tiếng Việt', '越', '越南文', 'vi', 'vie'],
  },
  // 15. 泰语
  {
    label: LanguageNameMap[LanguageEnum['th-TH']],
    value: LanguageEnum['th-TH'],
    aliases: ['泰语', 'Thai', 'ไทย', '泰', '泰文', '暹罗语', 'th', 'tha'],
  },
  // 16. 繁体中文
  {
    label: LanguageNameMap[LanguageEnum['zh-TW']],
    value: LanguageEnum['zh-TW'],
    aliases: [
      '繁体中文',
      '台湾中文',
      'Traditional Chinese',
      'Taiwan Chinese',
      '繁中',
      '台语',
      '台湾话',
      '正体中文',
      'tw',
      '繁',
    ],
  },
  {
    label: LanguageNameMap[LanguageEnum['zh-HK']],
    value: LanguageEnum['zh-HK'],
    aliases: [
      '香港中文',
      '繁体中文',
      'Hong Kong Chinese',
      'Traditional Chinese',
      '港中',
      '粤语',
      '广东话',
      '港语',
      'hk',
      '繁',
    ],
  },
  // 17. 印尼语
  {
    label: LanguageNameMap[LanguageEnum['id-ID']],
    value: LanguageEnum['id-ID'],
    aliases: [
      '印尼语',
      '印度尼西亚语',
      'Indonesian',
      'Bahasa Indonesia',
      '印',
      '印尼文',
      'id',
      'ind',
    ],
  },
  // 18. 荷兰语
  {
    label: LanguageNameMap[LanguageEnum['nl-NL']],
    value: LanguageEnum['nl-NL'],
    aliases: ['荷兰语', 'Dutch', 'Nederlands', '荷', '荷兰文', '尼德兰语', 'nl', 'nld', 'dut'],
  },
  // 19. 波兰语
  {
    label: LanguageNameMap[LanguageEnum['pl-PL']],
    value: LanguageEnum['pl-PL'],
    aliases: ['波兰语', 'Polish', 'polski', '波', '波兰文', 'pl', 'pol'],
  },
  // 20. 乌克兰语
  {
    label: LanguageNameMap[LanguageEnum['uk-UA']],
    value: LanguageEnum['uk-UA'],
    aliases: ['乌克兰语', 'Ukrainian', 'українська', '乌', '乌克兰文', 'uk', 'ukr'],
  },
  // 21. 乌尔都语
  {
    label: LanguageNameMap[LanguageEnum.ur],
    value: LanguageEnum.ur,
    aliases: ['乌尔都语', 'Urdu', 'اردو', '巴基斯坦语', 'ur', 'urd'],
  },
  // 22. 马来语
  {
    label: LanguageNameMap[LanguageEnum['ms-MY']],
    value: LanguageEnum['ms-MY'],
    aliases: ['马来语', 'Malay', 'Bahasa Melayu', '马', '马来文', 'ms', 'msa', 'may'],
  },
  // 23. 他加禄语
  {
    label: LanguageNameMap[LanguageEnum.tl],
    value: LanguageEnum.tl,
    aliases: ['他加禄语', '菲律宾语', 'Tagalog', 'Filipino', '菲语', 'tl', 'tgl', 'fil'],
  },
  // 24. 孟加拉语
  {
    label: LanguageNameMap[LanguageEnum['bn-BD']],
    value: LanguageEnum['bn-BD'],
    aliases: ['孟加拉语', 'Bengali', 'বাংলা', '孟', '孟加拉文', 'bn', 'ben'],
  },
  // 25. 瑞典语
  {
    label: LanguageNameMap[LanguageEnum['sv-SE']],
    value: LanguageEnum['sv-SE'],
    aliases: ['瑞典语', 'Swedish', 'svenska', '瑞', '瑞典文', 'sv', 'swe'],
  },
  // 26. 挪威语
  {
    label: LanguageNameMap[LanguageEnum['nb-NO']],
    value: LanguageEnum['nb-NO'],
    aliases: ['挪威语', 'Norwegian', 'norsk', '挪', '挪威文', 'nb', 'nor', 'nob'],
  },
  // 27. 丹麦语
  {
    label: LanguageNameMap[LanguageEnum['da-DK']],
    value: LanguageEnum['da-DK'],
    aliases: ['丹麦语', 'Danish', 'dansk', '丹', '丹麦文', 'da', 'dan'],
  },
  // 28. 芬兰语
  {
    label: LanguageNameMap[LanguageEnum['fi-FI']],
    value: LanguageEnum['fi-FI'],
    aliases: ['芬兰语', 'Finnish', 'suomi', '芬', '芬兰文', 'fi', 'fin'],
  },
  // 29. 希腊语
  {
    label: LanguageNameMap[LanguageEnum['el-GR']],
    value: LanguageEnum['el-GR'],
    aliases: ['希腊语', 'Greek', 'Ελληνικά', '希', '希腊文', '古希腊语', 'el', 'ell', 'gre'],
  },
  // 30. 捷克语
  {
    label: LanguageNameMap[LanguageEnum['cs-CZ']],
    value: LanguageEnum['cs-CZ'],
    aliases: ['捷克语', 'Czech', 'čeština', '捷', '捷克文', '波希米亚语', 'cs', 'ces', 'cze'],
  },
  // 31. 匈牙利语
  {
    label: LanguageNameMap[LanguageEnum['hu-HU']],
    value: LanguageEnum['hu-HU'],
    aliases: ['匈牙利语', 'Hungarian', 'magyar', '匈', '匈牙利文', '马扎尔语', 'hu', 'hun'],
  },
  // 32. 罗马尼亚语
  {
    label: LanguageNameMap[LanguageEnum['ro-RO']],
    value: LanguageEnum['ro-RO'],
    aliases: ['罗马尼亚语', 'Romanian', 'română', '罗', '罗马尼亚文', 'ro', 'ron', 'rum'],
  },
  // 33. 保加利亚语
  {
    label: LanguageNameMap[LanguageEnum['bg-BG']],
    value: LanguageEnum['bg-BG'],
    aliases: ['保加利亚语', 'Bulgarian', 'български', '保', '保加利亚文', 'bg', 'bul'],
  },
  // 34. 克罗地亚语
  {
    label: LanguageNameMap[LanguageEnum['hr-HR']],
    value: LanguageEnum['hr-HR'],
    aliases: ['克罗地亚语', 'Croatian', 'hrvatski', '克', '克罗地亚文', 'hr', 'hrv'],
  },
  // 35. 塞尔维亚语
  {
    label: LanguageNameMap[LanguageEnum['sr-RS']],
    value: LanguageEnum['sr-RS'],
    aliases: ['塞尔维亚语', 'Serbian', 'српски', '塞', '塞尔维亚文', 'sr', 'srp'],
  },
  // 36. 波斯尼亚语
  {
    label: LanguageNameMap[LanguageEnum['bs-BA']],
    value: LanguageEnum['bs-BA'],
    aliases: ['波斯尼亚语', 'Bosnian', 'bosanski', '波斯', '波斯尼亚文', 'bs', 'bos'],
  },
  // 37. 斯洛伐克语
  {
    label: LanguageNameMap[LanguageEnum['sk-SK']],
    value: LanguageEnum['sk-SK'],
    aliases: ['斯洛伐克语', 'Slovak', 'slovenčina', '斯洛伐克', '斯洛伐克文', 'sk', 'slk', 'slo'],
  },
  // 38. 斯洛文尼亚语
  {
    label: LanguageNameMap[LanguageEnum['sl-SI']],
    value: LanguageEnum['sl-SI'],
    aliases: [
      '斯洛文尼亚语',
      'Slovenian',
      'slovenščina',
      '斯洛文尼亚',
      '斯洛文尼亚文',
      'sl',
      'slv',
    ],
  },
  // 39. 立陶宛语
  {
    label: LanguageNameMap[LanguageEnum.lt],
    value: LanguageEnum.lt,
    aliases: ['立陶宛语', 'Lithuanian', 'lietuvių', '立', '立陶宛文', 'lt', 'lit'],
  },
  // 40. 拉脱维亚语
  {
    label: LanguageNameMap[LanguageEnum['lv-LV']],
    value: LanguageEnum['lv-LV'],
    aliases: ['拉脱维亚语', 'Latvian', 'latviešu', '拉脱维亚', '拉脱维亚文', 'lv', 'lav'],
  },
  // 41. 爱沙尼亚语
  {
    label: LanguageNameMap[LanguageEnum['et-EE']],
    value: LanguageEnum['et-EE'],
    aliases: ['爱沙尼亚语', 'Estonian', 'eesti', '爱沙尼亚', '爱沙尼亚文', 'et', 'est'],
  },
  // 42. 哈萨克语
  {
    label: LanguageNameMap[LanguageEnum.kk],
    value: LanguageEnum.kk,
    aliases: ['哈萨克语', 'Kazakh', 'қазақ тілі', '哈', '哈萨克文', 'kk', 'kaz'],
  },
  // 43. 阿尔巴尼亚语
  {
    label: LanguageNameMap[LanguageEnum['sq-AL']],
    value: LanguageEnum['sq-AL'],
    aliases: [
      '阿尔巴尼亚语',
      'Albanian',
      'shqip',
      '阿尔巴尼亚',
      '阿尔巴尼亚文',
      'sq',
      'sqi',
      'alb',
    ],
  },
  // 44. 马其顿语
  {
    label: LanguageNameMap[LanguageEnum['mk-MK']],
    value: LanguageEnum['mk-MK'],
    aliases: ['马其顿语', 'Macedonian', 'македонски', '马其顿', '马其顿文', 'mk', 'mkd', 'mac'],
  },
  // 45. 蒙古语
  {
    label: LanguageNameMap[LanguageEnum.mn],
    value: LanguageEnum.mn,
    aliases: ['蒙古语', 'Mongolian', 'монгол', '蒙', '蒙古文', 'mn', 'mon'],
  },
  // 46. 冰岛语
  {
    label: LanguageNameMap[LanguageEnum['is-IS']],
    value: LanguageEnum['is-IS'],
    aliases: ['冰岛语', 'Icelandic', 'íslenska', '冰', '冰岛文', 'is', 'isl', 'ice'],
  },
  // 47. 亚美尼亚语
  {
    label: LanguageNameMap[LanguageEnum['hy-AM']],
    value: LanguageEnum['hy-AM'],
    aliases: ['亚美尼亚语', 'Armenian', 'հայերեն', '亚美尼亚', '亚美尼亚文', 'hy', 'hye', 'arm'],
  },
  // 48. 格鲁吉亚语
  {
    label: LanguageNameMap[LanguageEnum['ka-GE']],
    value: LanguageEnum['ka-GE'],
    aliases: ['格鲁吉亚语', 'Georgian', 'ქართული', '格鲁吉亚', '格鲁吉亚文', 'ka', 'kat', 'geo'],
  },
  // 49. 马拉地语
  {
    label: LanguageNameMap[LanguageEnum['mr-IN']],
    value: LanguageEnum['mr-IN'],
    aliases: ['马拉地语', 'Marathi', 'मराठी', '马拉地', '马拉地文', 'mr', 'mar'],
  },
  // 50. 古吉拉特语
  {
    label: LanguageNameMap[LanguageEnum['gu-IN']],
    value: LanguageEnum['gu-IN'],
    aliases: ['古吉拉特语', 'Gujarati', 'ગુજરાતી', '古吉拉特', '古吉拉特文', 'gu', 'guj'],
  },
  // 51. 泰米尔语
  {
    label: LanguageNameMap[LanguageEnum['ta-IN']],
    value: LanguageEnum['ta-IN'],
    aliases: ['泰米尔语', 'Tamil', 'தமிழ்', '泰米尔', '泰米尔文', 'ta', 'tam'],
  },
  // 52. 泰卢固语
  {
    label: LanguageNameMap[LanguageEnum['te-IN']],
    value: LanguageEnum['te-IN'],
    aliases: ['泰卢固语', 'Telugu', 'తెలుగు', '泰卢固', '泰卢固文', 'te', 'tel'],
  },
  // 53. 卡纳达语
  {
    label: LanguageNameMap[LanguageEnum['kn-IN']],
    value: LanguageEnum['kn-IN'],
    aliases: ['卡纳达语', 'Kannada', 'ಕನ್ನಡ', '卡纳达', '卡纳达文', 'kn', 'kan'],
  },
  // 54. 马拉雅拉姆语
  {
    label: LanguageNameMap[LanguageEnum.ml],
    value: LanguageEnum.ml,
    aliases: ['马拉雅拉姆语', 'Malayalam', 'മലയാളം', '马拉雅拉姆', '马拉雅拉姆文', 'ml', 'mal'],
  },
  // 55. 旁遮普语
  {
    label: LanguageNameMap[LanguageEnum.pa],
    value: LanguageEnum.pa,
    aliases: ['旁遮普语', 'Punjabi', 'ਪੰਜਾਬੀ', '旁遮普', '旁遮普文', 'pa', 'pan'],
  },
  // 56. 缅甸语
  {
    label: LanguageNameMap[LanguageEnum['my-MM']],
    value: LanguageEnum['my-MM'],
    aliases: ['缅甸语', 'Burmese', 'မြန်မာ', '缅', '缅甸文', 'my', 'mya', 'bur'],
  },
  // 57. 加泰罗尼亚语
  {
    label: LanguageNameMap[LanguageEnum['ca-ES']],
    value: LanguageEnum['ca-ES'],
    aliases: ['加泰罗尼亚语', 'Catalan', 'català', '加泰罗尼亚', '加泰罗尼亚文', 'ca', 'cat'],
  },
  // 58. 斯瓦希里语
  {
    label: LanguageNameMap[LanguageEnum['sw-TZ']],
    value: LanguageEnum['sw-TZ'],
    aliases: ['斯瓦希里语', 'Swahili', 'Kiswahili', '斯瓦希里', '斯瓦希里文', 'sw', 'swa'],
  },
  // 59. 阿姆哈拉语
  {
    label: LanguageNameMap[LanguageEnum.am],
    value: LanguageEnum.am,
    aliases: [
      '阿姆哈拉语',
      'Amharic',
      'አማርኛ',
      '阿姆哈拉',
      '阿姆哈拉文',
      '埃塞俄比亚语',
      'am',
      'amh',
    ],
  },
  // 60. 索马里语
  {
    label: LanguageNameMap[LanguageEnum['so-SO']],
    value: LanguageEnum['so-SO'],
    aliases: ['索马里语', 'Somali', 'Soomaali', '索马里', '索马里文', 'so', 'som'],
  },
];

// TEMPORARY: Commented out franc-min function for Cloudflare Workers compatibility
// This is a simple fallback implementation that only does basic character detection
function simpleLanguageDetect(text: string): string {
  // 首先检测是否包含日语特有字符
  const hiraganaRegex = /[\u3040-\u309f]/g; // 平假名
  const katakanaRegex = /[\u30a0-\u30ff]/g; // 片假名
  const hiraganaCount = (text.match(hiraganaRegex) || []).length;
  const katakanaCount = (text.match(katakanaRegex) || []).length;
  const japaneseKanaCount = hiraganaCount + katakanaCount;

  // 如果检测到日语假名，很可能是日语
  if (japaneseKanaCount > 0) {
    const hanziRegex = /[\u4e00-\u9fa5]/g;
    const hanziCount = (text.match(hanziRegex) || []).length;

    // 如果假名数量与汉字数量比例较高，说明更可能是日语
    if (
      japaneseKanaCount / Math.max(hanziCount, 1) > 0.3 ||
      japaneseKanaCount / text.length > 0.1
    ) {
      return 'jpn'; // Japanese
    }
  }

  // 检测中文汉字
  const chineseRegex = /[\u4e00-\u9fa5]/g;
  const chineseMatches = text.match(chineseRegex) || [];
  const chineseCount = chineseMatches.length;

  // 检测中文标点符号
  const chinesePunctuationRegex = /[，。！？：；""''【】（）「」]/g;
  const chinesePunctuationCount = (text.match(chinesePunctuationRegex) || []).length;

  // 计算中文相关字符总数
  const totalChineseRelated = chineseCount + chinesePunctuationCount;

  // 基于不同阈值检测中文
  if (
    // 如果中文字符占整个文本的比例超过20%
    (chineseCount > 0 && chineseCount / text.length > 0.2) ||
    // 或者短文本中有足够的中文字符
    (text.length < 50 && chineseCount >= 3) ||
    // 或者有中文标点符号
    (chinesePunctuationCount > 0 && totalChineseRelated / text.length > 0.15)
  ) {
    return 'zho'; // Chinese
  }

  // 检测阿拉伯文字符
  const arabicRegex = /[\u0600-\u06ff]/g;
  if ((text.match(arabicRegex) || []).length > 0) {
    return 'ara'; // Arabic
  }

  // 检测韩文字符
  const koreanRegex = /[\uac00-\ud7af]/g;
  if ((text.match(koreanRegex) || []).length > 0) {
    return 'kor'; // Korean
  }

  // 默认返回英语
  return 'eng';
}

// TEMPORARY: Using simpler function instead of franc
function nomarlizeFranc(text: string) {
  return simpleLanguageDetect(text);
}

export function getLanguageEnum(text?: string) {
  if (!text) return undefined;
  const francDetect = nomarlizeFranc(text);

  // 映射到 iso6391
  const iso6391 = iso6393To1[francDetect] || 'en';

  // 映射到我们自己的语言枚举
  const language = iso6391ToLanguage[iso6391] || iso6391ToLanguage.en;
  return language as LanguageEnum;
}

export function francToIso6391(text: string) {
  if (!text) return undefined;
  const francDetect = nomarlizeFranc(text);
  const iso6391 = iso6393To1[francDetect] || 'en';
  return iso6391;
}

export function resolveLanguage(param: {
  language: AILanguageEnumKeys;
  content: ContentHandler | null;
  locale: LanguageEnum;
}) {
  const { content, locale, language } = param;
  if (language === AILanguageEnum.system) return locale;
  if (language === AILanguageEnum['follow-content']) {
    return content?.toVO()?.language || getLanguageEnum(content?.toPlainValue()) || locale;
  }
  return language;
}

export type LanguageKeys = keyof typeof LanguageEnum;

// 丢给 chatgpt 转换的
export const iso6391ToLanguage: Record<string, LanguageKeys> = {
  am: 'am',
  ar: 'ar',
  bg: 'bg-BG',
  bn: 'bn-BD',
  bs: 'bs-BA',
  ca: 'ca-ES',
  cs: 'cs-CZ',
  da: 'da-DK',
  de: 'de-DE',
  el: 'el-GR',
  en: 'en-US',
  es: 'es-ES',
  et: 'et-EE',
  fi: 'fi-FI',
  fr: 'fr-FR',
  gu: 'gu-IN',
  hi: 'hi-IN',
  hr: 'hr-HR',
  hu: 'hu-HU',
  hy: 'hy-AM',
  id: 'id-ID',
  is: 'is-IS',
  it: 'it-IT',
  ja: 'ja-JP',
  ka: 'ka-GE',
  kk: 'kk',
  kn: 'kn-IN',
  ko: 'ko-KR',
  lt: 'lt',
  lv: 'lv-LV',
  mk: 'mk-MK',
  ml: 'ml',
  mn: 'mn',
  mr: 'mr-IN',
  ms: 'ms-MY',
  my: 'my-MM',
  nb: 'nb-NO',
  nl: 'nl-NL',
  pa: 'pa',
  pl: 'pl-PL',
  pt: 'pt-PT',
  ro: 'ro-RO',
  ru: 'ru-RU',
  sk: 'sk-SK',
  sl: 'sl-SI',
  so: 'so-SO',
  sq: 'sq-AL',
  sr: 'sr-RS',
  sv: 'sv-SE',
  sw: 'sw-TZ',
  ta: 'ta-IN',
  te: 'te-IN',
  th: 'th-TH',
  tl: 'tl',
  tr: 'tr-TR',
  uk: 'uk-UA',
  ur: 'ur',
  vi: 'vi-VN',
  zh: 'zh-CN',
  'zh-HK': 'zh-HK',
  'zh-TW': 'zh-TW',
};
