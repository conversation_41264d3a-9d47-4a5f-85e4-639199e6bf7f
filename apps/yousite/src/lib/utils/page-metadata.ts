import { getTranslations } from 'next-intl/server';

export async function sharedOpenGraphMetadata(locale: string) {
  const t = await getTranslations({ locale, namespace: 'LocaleLayout' });

  return {
    title: t('title'),
    description: t('description'),
    url: 'https://youmind.com',
    type: 'website',
    siteName: 'YouMind',
    images: [{ url: '/cover.png' }],
  };
}

export async function sharedTwitterMetadata(locale: string) {
  const t = await getTranslations({ locale, namespace: 'LocaleLayout' });

  return {
    title: t('title'),
    description: t('description'),
    card: 'summary_large_image',
    images: [{ url: '/cover.png' }],
  };
}
