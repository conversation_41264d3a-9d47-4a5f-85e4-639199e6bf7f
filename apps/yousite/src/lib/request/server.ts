import { createCamelCaseApiClient } from '@repo/api';
import { cookies } from 'next/headers';
import { UserWithPreferenceVO } from '@/schema/userSchema';

export const createApiClientServerSide = () => {
  const cookieStore = cookies();

  return createCamelCaseApiClient({
    basePath:
      process.env.NODE_ENV === 'development'
        ? 'http://localhost:3003'
        : process.env.NEXT_PUBLIC_YOUMIND_API_BASE_PATH,
    headers: {
      'Content-Type': 'application/json',
      Cookie: cookieStore.toString(),
    },
  });
};

export async function tryGetCurrentUserFromServer() {
  const apiClient = createApiClientServerSide();
  try {
    const user = await apiClient.userApi.getCurrentUser();
    return user as UserWithPreferenceVO;
  } catch (error: any) {
    return null;
  }
}
