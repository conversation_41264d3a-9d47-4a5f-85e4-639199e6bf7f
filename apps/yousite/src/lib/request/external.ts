import { toast } from '@repo/ui/components/ui/sonner';
import * as Sentry from '@sentry/nextjs';
import { redirect } from 'next/navigation';
import { isErrorInfo, type RestErrorInfo } from '@/lib/error';
import { jumpToLogin } from '@/lib/hooks/useNotFound';
import { redirectTo404FromClient, redirectTo404FromServer } from '../utils/redirect';

const isPlainObject = (obj: unknown) =>
  obj !== null && typeof obj === 'object' && Object.getPrototypeOf(obj) === Object.prototype;

type FetchOptionsWithoutBody = Omit<NonNullable<Parameters<typeof fetch>[1]>, 'body'>;

const handleFetchOptions = (
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit | unknown[];
  } & FetchOptionsWithoutBody,
) => {
  const { body, ...restOptions } = options;
  const headers = new Headers(restOptions.headers);
  // 默认使用 application/json
  if (!headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  // 如果 body 是纯对象，并且 content-type 是 application/json，将 body 转换为 json 字符串
  const fetchBody =
    (isPlainObject(body) || Array.isArray(body)) &&
    headers.get('Content-Type')?.startsWith('application/json')
      ? JSON.stringify(body)
      : body;

  return { ...restOptions, headers, body: fetchBody as BodyInit };
};

async function handleErrorResponse(
  response: Response,
  options: {
    silent?: boolean;
    isInternalHTTPCall: boolean;
    noRedirect?: boolean;
  },
): Promise<{ error: RestErrorInfo }> {
  const contentType = response.headers.get('Content-Type');
  const isClient = typeof window !== 'undefined';
  const isInternalHTTPCall = options.isInternalHTTPCall;

  // 处理内部 API 调用
  if (isInternalHTTPCall) {
    if (contentType?.startsWith('application/json')) {
      const json = await response.json();
      if (isErrorInfo(json)) {
        if (json.status === 401 && !options.noRedirect) {
          isClient ? jumpToLogin() : redirect('/sign-in');
        } else if (json.status === 404 && !options.noRedirect) {
          isClient ? redirectTo404FromClient() : redirectTo404FromServer();
        } else {
          if (!options.silent && isClient) {
            toast.error('Oops, something went wrong.', {
              description: `${json.code}: ${json.message}`,
              duration: 10000,
            });
          }
          // !client 的情况，不做处理
        }
      } else {
        Sentry.captureException(json);
      }
      console.error('callHTTP error:', json);
      return { error: json };
    }

    const text = await response.text();
    const isTimeout = text.includes('FUNCTION_INVOCATION_TIMEOUT');
    const errorInfo = {
      status: isTimeout ? 504 : response.status,
      code: isTimeout ? 'FunctionInvocationTimeout' : response.statusText,
      message: isTimeout ? 'Function invocation timeout.' : text,
    } satisfies RestErrorInfo;
    if (isClient) {
      if (!options.silent) {
        toast.error('Oops, something went wrong.', {
          description: `${errorInfo.code}: ${errorInfo.message}`,
          duration: 10000,
        });
      }
    }
    Sentry.captureException(errorInfo);
    console.error('callHTTP error:', text);
    return { error: errorInfo };
  }

  // 处理外部 API 调用
  if (!options.silent && isClient) {
    toast.error('Oops, something went wrong.', {
      duration: 10000,
    });
  }

  const errorInfo: RestErrorInfo = {
    status: response.status,
    code: response.statusText,
    message: response.statusText,
  };

  if (contentType?.startsWith('application/json')) {
    const json = await response.json();
    errorInfo.detail = json;
    console.error('callHTTP error:', json);
    Sentry.captureException(errorInfo);
    return { error: errorInfo };
  }

  const text = await response.text();
  errorInfo.detail = text;
  console.error('callHTTP error:', text);
  Sentry.captureException(errorInfo);
  return { error: errorInfo };
}

export type HTTPResponse<T> =
  | { data: T; error?: undefined }
  | { error: RestErrorInfo; data?: undefined };

/**
 * 调用 HTTP 请求, 可用于内部或者外部请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns 返回结果为  { data: T, error?: RestErrorInfo }，
 * 如果是外部请求，error.detail中包含了解析后的 body，可以自行提取并针对处理
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function callHTTP<T = any>(
  url: string,
  options: {
    silent?: boolean;
    noRedirect?: boolean;
    body?: Record<string, unknown> | BodyInit | unknown[];
  } & FetchOptionsWithoutBody = {},
): Promise<HTTPResponse<T>> {
  try {
    const isInternalHTTPCall = url.startsWith('/api/');

    const handledOptions = handleFetchOptions(options);
    const response = await fetch(url, handledOptions);

    if (!response.ok) {
      return handleErrorResponse(response, {
        silent: options.silent,
        isInternalHTTPCall,
        noRedirect: options.noRedirect,
      });
    }

    // 成功的响应
    const contentType = response.headers.get('Content-Type');
    if (contentType?.startsWith('application/json')) {
      const data = await response.json();
      return { data };
    } else {
      const data = await response.text();
      return { data } as HTTPResponse<T>;
    }
  } catch (error) {
    // 处理网络错误等其他异常
    const errorInfo = {
      status: 500,
      code: error instanceof Error ? error.name : 'NetworkError',
      message: error instanceof Error ? error.message : 'Network error',
    } satisfies RestErrorInfo;
    // 上报 Sentry
    if (error instanceof Error && error.name === 'AbortError') {
      // 不上报 AbortError 到 Sentry, 来自用户的手动取消
      console.error('Request aborted by user:', error.message);
      return { error: errorInfo };
    }
    Sentry.captureException(error);
    if (!options.silent) {
      toast.error('Oops, something went wrong.', {
        description: `${errorInfo.code}: ${errorInfo.message}`,
        duration: 10000,
      });
    }

    return { error: errorInfo } as HTTPResponse<T>;
  }
}
