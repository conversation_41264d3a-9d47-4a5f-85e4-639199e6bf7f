'use client';

import { useRouter } from '@i18n/navigation';
import { atom, useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { redirectTo404FromClient } from '@/lib/utils/redirect';

export const JUMP_NOT_FOUND_MESSAGE_TYPE = 'jump_to_not_found';
export const JUMP_LOGIN_MESSAGE_TYPE = 'jump_to_login';

export const jumpToNotFound = () => {
  // 直接跳转到404页面，不使用postMessage
  redirectTo404FromClient();
};

export const jumpToLogin = () => {
  window.postMessage({
    type: JUMP_LOGIN_MESSAGE_TYPE,
  });
};

const listenToMessageAtom = atom<{
  type: string;
  listener?: (event: MessageEvent) => void;
}>({
  type: 'none',
});

export function useNotFound(level: string) {
  const router = useRouter();
  // 对于 not found，要专门在 render 里调用才能正确跳转
  const [jumpingToNotFound, setJumpingToNotFound] = useState(false);
  const [listenToMessage, setListenToMessage] = useAtom(listenToMessageAtom);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === JUMP_NOT_FOUND_MESSAGE_TYPE) {
        setJumpingToNotFound(true);
      }
      if (event.data?.type === JUMP_LOGIN_MESSAGE_TYPE) {
        router.replace('/sign-in');
      }
    };
    if (listenToMessage.type === 'none') {
      window.addEventListener('message', handleMessage);
      setListenToMessage({
        type: level,
        listener: handleMessage,
      });
    } else if (listenToMessage.type !== level) {
      // 更高优先级的消息监听需要覆盖全局级的消息监听
      if (listenToMessage.type === 'universal') {
        window.removeEventListener('message', listenToMessage.listener!);
        window.addEventListener('message', handleMessage);
        setListenToMessage({
          type: level,
          listener: handleMessage,
        });
      }
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    if (jumpingToNotFound) {
      setJumpingToNotFound(false);
    }
  }, [jumpingToNotFound]);

  if (jumpingToNotFound) {
    jumpToNotFound();
  }

  return null;
}
