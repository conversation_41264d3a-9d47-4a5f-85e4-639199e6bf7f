import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/** For local development identification only, DO NOT USE IN PRODUCTION */
export function isConnectingPreviewDB() {
  return process.env.DB_URL?.includes('kwqadfqbeayirfvglakk');
}

/** For local development identification only, DO NOT USE IN PRODUCTION */
export function isConnectingProdDB() {
  return process.env.DB_URL?.includes('flzdupptcpbcowdaetfq');
}
