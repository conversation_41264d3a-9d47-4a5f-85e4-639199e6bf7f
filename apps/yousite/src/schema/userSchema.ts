import { AILanguageEnum, DisplayLanguageEnum, LanguageEnum } from '@repo/common';
import { z } from 'zod';

// 域类型
export const UserPreferenceSchema = z.object({
  id: z.string().uuid(),
  createdAt: z.date().describe('创建时间'),
  updatedAt: z.date().describe('更新时间'),
  displayLanguage: z.nativeEnum(DisplayLanguageEnum).describe('UI 展示语言'),
  aiResponseLanguage: z.nativeEnum(AILanguageEnum).describe('AI 响应语言'),
  ai2ndResponseLanguage: z.nativeEnum(AILanguageEnum).describe('AI 第二响应语言'),
  enableBilingual: z.boolean().describe('是否开启双语模式'),
  detectedLanguage: z.nativeEnum(LanguageEnum).nullable().describe('最新检测到的语言'),
});

export type UserPreference = z.infer<typeof UserPreferenceSchema>;

export const PatchUserPreferenceParamSchema = UserPreferenceSchema.pick({
  id: true,
})
  .required()
  .merge(
    UserPreferenceSchema.pick({
      displayLanguage: true,
      aiResponseLanguage: true,
      ai2ndResponseLanguage: true,
      enableBilingual: true,
      detectedLanguage: true,
    }).partial(),
  );

export type PatchUserPreferenceParam = z.infer<typeof PatchUserPreferenceParamSchema>;

// App 类型（来自 app/user/types.ts）
// @chuck fixme: 需要从 dao/user/types 导入 UserOnboardStatusEnum
export const UserVOSchema = z.object({
  id: z.string().uuid().describe('唯一标识'),
  createdAt: z.date().describe('创建时间'),
  updatedAt: z.date().describe('更新时间'),
  email: z.string().email().describe('邮箱'),
  name: z.string().describe('用户名'),
  picture: z.string().url().optional().describe('头像地址'),
  profile: z.object({}).optional(),
  // @chuck fixme: 需要导入 UserOnboardStatusEnum 后取消注释
  // onboardStatus: z
  //   .nativeEnum(UserOnboardStatusEnum)
  //   .default(UserOnboardStatusEnum.WAITING)
  //   .describe("注册状态"),
  timeZone: z.string().optional().describe('时区'),
  confirmedAt: z.date().optional().describe('确认时间'),
  lastSignInAt: z.date().optional().describe('最后登录时间'),
});

export const UserPreferenceVOSchema = UserPreferenceSchema.omit({
  detectedLanguage: true,
});
export type UserPreferenceVO = z.infer<typeof UserPreferenceVOSchema>;

export const UserWithPreferenceVOSchema = UserVOSchema.extend({
  preference: UserPreferenceVOSchema.pick({
    displayLanguage: true,
    aiResponseLanguage: true,
    ai2ndResponseLanguage: true,
    enableBilingual: true,
  }),
});

export type UserVO = z.infer<typeof UserVOSchema> & {
  profile?: Record<string, string | string[]>;
};

export type UserWithPreferenceVO = z.infer<typeof UserWithPreferenceVOSchema>;

export const PatchUserNameParamSchema = z.object({
  name: z.string(),
});

export type PatchUserNameParam = z.infer<typeof PatchUserNameParamSchema>;

export const PatchUserAvatarParamSchema = z.object({
  avatarUrl: z.string(),
});

export type PatchUserAvatarParam = z.infer<typeof PatchUserAvatarParamSchema>;

export const PatchUserProfileParamSchema = z
  .object({
    skip: z.string().optional(),
    content: z.array(z.string()).optional(),
    tools: z.array(z.string()).optional(),
    valuation: z.string().optional(),
    purpose: z.array(z.string()).optional(),
    challenge: z.string().optional(),
  })
  .refine(
    (data: { [key: string]: string | string[] | undefined }) => {
      const keys = Object.keys(data).filter((k) => k !== 'skip');
      return keys.some((key) => {
        const value = data[key];
        if (value === undefined) return false;
        if (typeof value === 'string') {
          return !!value;
        }
        if (Array.isArray(value)) {
          return !!value.length;
        }
        return false;
      });
    },
    {
      message: 'At least one property must be filled',
    },
  );
export type PatchUserProfileParam = z.infer<typeof PatchUserProfileParamSchema>;

export const PatchUserPreferenceByCurrentUserParamSchema = PatchUserPreferenceParamSchema.omit({
  id: true,
});

export type PatchUserPreferenceByCurrentUserParam = z.infer<
  typeof PatchUserPreferenceByCurrentUserParamSchema
>;

export const signInWithOTPSchema = z.object({
  email: z.string().email(),
});
export type SignInWithOTPParam = z.infer<typeof signInWithOTPSchema>;

export const validateWithOTPSchema = z.object({
  email: z.string().email(),
  token: z
    .array(z.string().min(1)) // 保证每个字符串非空
    .length(6) // 保证数组正好 6 个元素
    .transform((arr) => arr.map(Number)) // 转为数字
    .refine((arr) => arr.every((num) => !Number.isNaN(num))),
});
export type ValidateWithOTPParam = {
  email: string;
  token: string[];
};

export const validateWithMagicLinkSchema = z.object({
  tokenHash: z.string(),
});
export type ValidateWithMagicLinkParam = z.infer<typeof validateWithMagicLinkSchema>;
