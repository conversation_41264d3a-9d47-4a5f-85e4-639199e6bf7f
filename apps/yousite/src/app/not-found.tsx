'use client';

import { NotFound } from '@repo/ui/components/custom/not-found';
import { useRouter } from 'next/router';

// Render the default Next.js 404 page when a route
// is requested that doesn't match the middleware and
// therefore doesn't have a locale associated with it.

// 理论上不应该走到这里来，除了本地
export default function NotFoundPage() {
  const router = useRouter();
  return (
    <html lang="en">
      <body>
        <NotFound
          onGoHomeClick={() => {
            router.push('/');
          }}
          onLogoClick={() => {
            router.push('/');
          }}
        />
      </body>
    </html>
  );
}
