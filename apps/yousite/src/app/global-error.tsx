'use client';

import { InternalError } from '@repo/ui/components/custom/internal-error';
import { Toaster } from '@repo/ui/components/ui/sonner';
import './globals.css';
import '@repo/ui/globals.css';
import * as Sentry from '@sentry/nextjs';
import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // 根据error.digest判断是否来自服务端
  const from = error.digest ? 'server' : 'client';

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <InternalError error={error} from={from} />
        <Toaster />
      </body>
    </html>
  );
}
