import { routing } from '@i18n/routing';
import { Toaster } from '@repo/ui/components/ui/sonner';
import { isDev, isPreview } from '@youmindinc/youcommon';
import Script from 'next/script';
import { hasLocale, NextIntlClientProvider } from 'next-intl';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { ThemeProvider } from 'next-themes';
import type { ReactNode } from 'react';
import { inter } from '@/lib/fonts';
import { isConnectingPreviewDB, isConnectingProdDB } from '@/lib/utils';
import { sharedOpenGraphMetadata, sharedTwitterMetadata } from '@/lib/utils/page-metadata';
import { redirectTo404FromServer } from '@/lib/utils/redirect';

type Props = {
  children: ReactNode;
  params: { locale: string };
};

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params }: Omit<Props, 'children'>) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'LocaleLayout' });
  const isProdDB = isConnectingProdDB();
  const isPreviewDB = isConnectingPreviewDB();

  let icon = `/favicon.ico`;
  if (isDev()) {
    if (isProdDB) {
      icon = `/favicon/development-prod-db.svg`;
    } else if (isPreviewDB) {
      icon = `/favicon/development-pre-db.svg`;
    } else {
      icon = `/favicon/development.ico`;
    }
  } else if (isPreview()) {
    icon = `/favicon/preview.ico`;
  }

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    icons: {
      icon,
    },
    applicationName: 'YouMind',
    openGraph: await sharedOpenGraphMetadata(locale),
    twitter: await sharedTwitterMetadata(locale),
  };
}

export default async function LocaleLayout({ children, params }: Props) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    redirectTo404FromServer();
  }
  // Enable static rendering
  setRequestLocale(locale);

  // [TODO] 添加 PostHog

  return (
    /**
     * https://github.com/pacocoursey/next-themes#readme
     * Note! If you do not add suppressHydrationWarning to your <html> you will get warnings because next-themes updates that element.
     * This property only applies one level deep, so it won't block hydration warnings on other elements.
     */
    <html lang={locale} suppressHydrationWarning className={`${inter.variable} font-sans`}>
      <head></head>
      <body translate="no" suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          forcedTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <NextIntlClientProvider>
            {children}
            <Toaster />
          </NextIntlClientProvider>
        </ThemeProvider>
        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GOOGLE_ANALYTICS_TAG_ID}`}
        />
      </body>
    </html>
  );
}
