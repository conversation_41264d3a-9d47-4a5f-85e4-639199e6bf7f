import { revalidatePath, revalidateTag } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { tags, paths, token } = await request.json();

    // 验证安全令牌，避免未授权的重新验证
    const secretToken = process.env.REVALIDATE_TOKEN;
    if (!secretToken || token !== secretToken) {
      return NextResponse.json({ success: false, message: 'Unauthorized access' }, { status: 401 });
    }

    // 处理标签重新验证
    if (tags && Array.isArray(tags) && tags.length > 0) {
      for (const tag of tags) {
        revalidateTag(tag);
      }
    }

    // 处理路径重新验证
    if (paths && Array.isArray(paths) && paths.length > 0) {
      for (const path of paths) {
        revalidatePath(path);
      }
    }

    // 如果没有提供任何标签或路径，返回错误
    if ((!tags || !tags.length) && (!paths || !paths.length)) {
      return NextResponse.json(
        { success: false, message: 'No valid tags or paths provided' },
        { status: 400 },
      );
    }

    return NextResponse.json({
      success: true,
      revalidated: true,
      date: new Date().toISOString(),
      revalidatedTags: tags || [],
      revalidatedPaths: paths || [],
    });
  } catch (error) {
    console.error('Revalidation failed:', error);
    return NextResponse.json(
      { success: false, message: 'Error during revalidation process' },
      { status: 500 },
    );
  }
}
