import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();
const nextConfig: NextConfig = {
  eslint: {
    dirs: ['src'],
  },
  transpilePackages: ['@repo/ui'],
  poweredByHeader: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.gooo.ai',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // 开发环境代理配置
  ...(process.env.NODE_ENV === 'development' && {
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:4000/api/:path*',
        },
        {
          source: '/auth/:path*',
          destination: 'http://localhost:4000/auth/:path*',
        },
      ];
    },
    async redirects() {
      return [
        {
          source: '/boards/:path*',
          destination: 'http://localhost:2000/boards/:path*',
          permanent: true,
        },
      ];
    },
  }),
};

export default withNextIntl(nextConfig);

// Initialize OpenNext for local development
if (process.env.NODE_ENV === 'development') {
  try {
    const { initOpenNextCloudflareForDev } = require('@opennextjs/cloudflare');
    initOpenNextCloudflareForDev();
  } catch (error: any) {
    console.warn('OpenNext Cloudflare dev initialization failed:', error.message);
  }
}
